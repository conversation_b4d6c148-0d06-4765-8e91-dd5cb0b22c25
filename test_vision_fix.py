#!/usr/bin/env python3
"""
测试Vision功能修复的脚本
用于验证图片caption生成是否正常工作
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from mineru_pipeline_all import item_to_markdown

def test_vision_config():
    """测试Vision配置是否正确"""
    print("=== Vision配置测试 ===")
    
    api_key = os.getenv("LOCAL_API_KEY")
    base_url = os.getenv("LOCAL_BASE_URL")
    vision_model = os.getenv("LOCAL_VISION_MODEL")
    
    print(f"API Key: {'✓ 存在' if api_key else '✗ 缺失'}")
    print(f"Base URL: {base_url or '✗ 缺失'}")
    print(f"Vision Model: {vision_model or '✗ 缺失'}")
    
    if not all([api_key, base_url, vision_model]):
        print("⚠️  环境变量配置不完整")
        return False
    
    print("✅ 环境变量配置正确")
    return True

def test_image_processing():
    """测试图片处理功能"""
    print("\n=== 图片处理测试 ===")
    
    # 查找一个测试图片
    test_image_dirs = [
        "data_base_json_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/auto/images/",
        "data_base_json_content/艾力斯-公司深度报告商业化成绩显著产品矩阵持续拓宽-25070718页/艾力斯-公司深度报告商业化成绩显著产品矩阵持续拓宽-25070718页/auto/images/"
    ]
    
    test_image = None
    for image_dir in test_image_dirs:
        if os.path.exists(image_dir):
            images = list(Path(image_dir).glob("*.jpg"))
            if images:
                test_image = str(images[0])
                break
    
    if not test_image:
        print("✗ 找不到测试图片")
        return False
    
    print(f"找到测试图片: {test_image}")
    
    # 创建测试item
    test_item = {
        "type": "image",
        "img_path": test_image,
        "image_caption": ["图表标题示例"],  # 模拟已有的简单caption
        "page_idx": 1
    }
    
    try:
        print("\n开始测试图片分析...")
        result = item_to_markdown(test_item, enable_image_caption=True)
        print(f"✅ 图片处理成功")
        print(f"生成的Markdown长度: {len(result)} 字符")
        
        # 检查caption是否被增强
        if 'AI描述' in str(test_item.get('image_caption', [])):
            print("✅ Caption已被AI增强")
        else:
            print("⚠️  Caption未被增强，可能有问题")
            
        return True
        
    except Exception as e:
        print(f"✗ 图片处理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Vision功能修复测试\n")
    
    # 测试配置
    config_ok = test_vision_config()
    if not config_ok:
        print("\n❌ 配置测试失败，请检查.env文件")
        return False
    
    # 测试图片处理
    processing_ok = test_image_processing()
    if not processing_ok:
        print("\n❌ 图片处理测试失败")
        return False
    
    print("\n🎉 所有测试通过！Vision功能修复成功")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)