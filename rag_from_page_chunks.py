import json
import os
import hashlib
from typing import List, Dict, Any
from tqdm import tqdm
import sys
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from sentence_transformers import SentenceTransformer

# --- 本地部署不再需要以下API相关的库 ---
# from get_text_embedding import get_text_embedding
# from dotenv import load_dotenv
# from openai import OpenAI
# load_dotenv()

class PageChunkLoader:
    def __init__(self, json_path: str):
        self.json_path = json_path
    def load_chunks(self) -> List[Dict[str, Any]]:
        with open(self.json_path, 'r', encoding='utf-8') as f:
            return json.load(f)

# ####################################################################
# ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼【修改部分 1: EmbeddingModel 类】▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
# ####################################################################
class EmbeddingModel:
    """
    使用本地模型进行向量化的类
    """
    def __init__(self, model_name: str = 'BAAI/bge-m3', batch_size: int = 32):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Embedding model is running on: {self.device}")
        # 从本地缓存加载模型, sentence-transformers会自动处理 ~/.cache/huggingface/hub/ 目录
        self.model = SentenceTransformer(model_name, device=self.device)
        self.batch_size = batch_size

    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        print(f"Embedding {len(texts)} texts using local model...")
        return self.model.encode(
            texts,
            batch_size=self.batch_size,
            convert_to_numpy=True,
            show_progress_bar=True  # 显示一个进度条
        ).tolist()

    def embed_text(self, text: str) -> List[float]:
        return self.embed_texts([text])[0]
# ####################################################################
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲【修改部分 1: 结束】▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
# ####################################################################


class SimpleVectorStore:
    def __init__(self):
        self.embeddings = []
        self.chunks = []
    def add_chunks(self, chunks: List[Dict[str, Any]], embeddings: List[List[float]]):
        self.chunks.extend(chunks)
        self.embeddings.extend(embeddings)
    def search(self, query_embedding: List[float], top_k: int = 3) -> List[Dict[str, Any]]:
        from numpy import dot
        from numpy.linalg import norm
        import numpy as np
        if not self.embeddings:
            return []
        emb_matrix = np.array(self.embeddings)
        query_emb = np.array(query_embedding)
        sims = emb_matrix @ query_emb / (norm(emb_matrix, axis=1) * norm(query_emb) + 1e-8)
        idxs = sims.argsort()[::-1][:top_k]
        return [self.chunks[i] for i in idxs]

# ####################################################################
# ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼【修改部分 2: SimpleRAG 类】▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
# ####################################################################
class SimpleRAG:
    def __init__(self, chunk_json_path: str, batch_size: int = 8):
        self.loader = PageChunkLoader(chunk_json_path)
        # 使用上面修改过的本地EmbeddingModel
        self.embedding_model = EmbeddingModel(batch_size=batch_size)
        self.vector_store = SimpleVectorStore()
        
        # --- 在初始化时就加载大语言模型 (LLM) ---
        self.llm_device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Text generation model will run on: {self.llm_device}")
        
        # 您缓存中的模型文件夹名, transformers会自动处理
        # 确保 Qwen/Qwen3-8B 已经被成功下载到缓存
        self.llm_model_name = 'Qwen/Qwen3-8B' 
        self.tokenizer = AutoTokenizer.from_pretrained(self.llm_model_name, trust_remote_code=True)
        
        print("Loading text generation model... (This may take a while and consume a lot of VRAM)")
        # 使用4-bit量化加载以节省显存
        self.llm_model = AutoModelForCausalLM.from_pretrained(
            self.llm_model_name,
            torch_dtype="auto",
            device_map="auto",
            trust_remote_code=True,
            load_in_4bit=True 
        )
        self.llm_model.eval() # 设置为评估模式
        print("Text generation model loaded successfully!")

    def setup(self):
        print("加载所有页chunk...")
        chunks = self.loader.load_chunks()
        print(f"共加载 {len(chunks)} 个chunk")
        print("生成嵌入 (embedding)...")
        embeddings = self.embedding_model.embed_texts([c['content'] for c in chunks])
        print("存储向量...")
        self.vector_store.add_chunks(chunks, embeddings)
        print("RAG向量库构建完成！")

    def query(self, question: str, top_k: int = 3) -> Dict[str, Any]:
        q_emb = self.embedding_model.embed_text(question)
        results = self.vector_store.search(q_emb, top_k)
        return {
            "question": question,
            "chunks": results
        }

    def generate_answer(self, question: str, top_k: int = 3) -> Dict[str, Any]:
        """
        本地检索 + 本地大模型生成式回答
        """
        q_emb = self.embedding_model.embed_text(question)
        chunks = self.vector_store.search(q_emb, top_k)
        
        context = "\n".join([
            f"[文件名]{c['metadata']['file_name']} [页码]{c['metadata']['page']}\n{c['content']}" for c in chunks
        ])
        
        prompt = (
            f"你是一名专业的金融分析助手，请根据以下检索到的内容回答用户问题。\n"
            f"请严格按照如下JSON格式输出：\n"
            f'{{"answer": "你的简洁回答", "filename": "来源文件名", "page": "来源页码"}}'"\n"
            f"检索内容：\n{context}\n\n问题：{question}\n"
            f"请确保输出内容为合法JSON字符串，不要输出多余内容。"
        )

        messages = [
            {"role": "system", "content": "你是一名专业的金融分析助手。"},
            {"role": "user", "content": prompt}
        ]
        text_prompt = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        model_inputs = self.tokenizer([text_prompt], return_tensors="pt").to(self.llm_device)

        # 使用本地模型进行生成
        with torch.no_grad():
            generated_ids = self.llm_model.generate(
                model_inputs.input_ids,
                max_new_tokens=1024
            )
        
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
        ]
        
        raw = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        
        # --- 以下是您原来的JSON解析和返回逻辑，保持不变 ---
        sys.path.append(os.path.dirname(__file__))
        from extract_json_array import extract_json_array
        
        json_str = extract_json_array(raw, mode='objects')
        if json_str:
            try:
                # json.loads() 可能会返回一个列表或字典, 这里统一处理
                parsed_json = json.loads(json_str)
                # 如果是列表, 取第一个元素; 如果是字典, 直接使用
                j = parsed_json[0] if isinstance(parsed_json, list) and parsed_json else parsed_json
                
                if isinstance(j, dict):
                    answer = j.get('answer', raw) # 如果解析不出answer, 返回原始文本
                    filename = j.get('filename', '')
                    page = j.get('page', '')
                else: # 解析出来不是字典或列表为空
                    answer = raw
                    filename = chunks[0]['metadata']['file_name'] if chunks else ''
                    page = chunks[0]['metadata']['page'] if chunks else ''
            except Exception:
                answer = raw
                filename = chunks[0]['metadata']['file_name'] if chunks else ''
                page = chunks[0]['metadata']['page'] if chunks else ''
        else:
            answer = raw
            filename = chunks[0]['metadata']['file_name'] if chunks else ''
            page = chunks[0]['metadata']['page'] if chunks else ''
        
        # 结构化输出
        return {
            "question": question,
            "answer": answer,
            "filename": filename,
            "page": page,
            "retrieval_chunks": chunks
        }
# ####################################################################
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲【修改部分 2: 结束】▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
# ####################################################################


if __name__ == '__main__':
    # 路径可根据实际情况调整
    chunk_json_path = os.path.join(os.path.dirname(__file__), 'all_pdf_page_chunks.json')
    # 删除了 model_path 参数，因为模型在类初始化时加载
    rag = SimpleRAG(chunk_json_path)
    rag.setup()

    # 控制测试时读取的题目数量，默认只随机抽取10个，实际跑全部时设为None
    TEST_SAMPLE_NUM = None # 设置为None则全部跑
    FILL_UNANSWERED = True  # 未回答的也输出默认内容

    # 批量评测脚本：读取测试集，检索+大模型生成，输出结构化结果
    # 注意: 测试集文件名可能需要根据您的实际文件名修改
    test_path = os.path.join(os.path.dirname(__file__), 'datas/test.json')
    if os.path.exists(test_path):
        with open(test_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        import concurrent.futures
        import random

        all_indices = list(range(len(test_data)))
        selected_indices = all_indices
        if TEST_SAMPLE_NUM is not None and TEST_SAMPLE_NUM > 0:
            if len(test_data) > TEST_SAMPLE_NUM:
                selected_indices = sorted(random.sample(all_indices, TEST_SAMPLE_NUM))

        def process_one(idx):
            item = test_data[idx]
            question = item['question']
            tqdm.write(f"[{selected_indices.index(idx)+1}/{len(selected_indices)}] 正在处理: {question[:30]}...")
            result = rag.generate_answer(question, top_k=5)
            return idx, result

        results = []
        if selected_indices:
            # 注意: 本地GPU推理时, 并发数应设为1, 否则会爆显存。
            # 如果您想实现更复杂的批量推理（batching），需要进一步修改generate_answer方法
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                results = list(tqdm(executor.map(process_one, selected_indices), total=len(selected_indices), desc='批量生成'))

        # 先输出一份未过滤的原始结果（含 idx）
        raw_out_path = os.path.join(os.path.dirname(__file__), 'rag_top1_pred_raw.json')
        with open(raw_out_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f'已输出原始未过滤结果到: {raw_out_path}')

        # 只保留结果部分，并去除 retrieval_chunks 字段
        idx2result = {idx: {k: v for k, v in r.items() if k != 'retrieval_chunks'} for idx, r in results}
        filtered_results = []
        for idx, item in enumerate(test_data):
            if idx in idx2result:
                filtered_results.append(idx2result[idx])
            elif FILL_UNANSWERED:
                filtered_results.append({
                    "question": item.get("question", ""),
                    "answer": "",
                    "filename": "",
                    "page": "",
                })
        # 输出结构化结果到json
        out_path = os.path.join(os.path.dirname(__file__), 'rag_top1_pred.json')
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_results, f, ensure_ascii=False, indent=2)
        print(f'已输出结构化检索+大模型生成结果到: {out_path}')