#!/usr/bin/env python3
"""
优化版RAG系统 - 修复prompt避免输出思考过程
混合检索 + 重排序 + 优化Prompt
"""

import os
import json
import jieba
import torch
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
from transformers import AutoTokenizer, AutoModel, AutoModelForCausalLM
from rank_bm25 import BM25Okapi
from sentence_transformers import CrossEncoder

class DenseRetriever:
    """密集检索器 - 使用向量相似度"""
    def __init__(self, model_name: str = "BAAI/bge-m3"):
        print(f"Loading dense retrieval model: {model_name}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        self.chunks = []
        self.embeddings = None
        
    def encode_text(self, text: str) -> np.ndarray:
        """编码文本为向量"""
        inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512, padding=True)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
        
        return embeddings.cpu().numpy()
    
    def build_index(self, chunks: List[Dict]):
        """构建向量索引"""
        print("Building dense index...")
        self.chunks = chunks
        texts = [chunk['content'] for chunk in chunks]
        
        all_embeddings = []
        batch_size = 32
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_embeddings = []
            for text in batch_texts:
                embedding = self.encode_text(text)
                batch_embeddings.append(embedding)
            all_embeddings.extend(batch_embeddings)
        
        self.embeddings = np.vstack(all_embeddings)
        print(f"Dense index built with {len(self.chunks)} chunks")
    
    def search(self, query: str, top_k: int = 10) -> List[Dict]:
        """搜索相似文档"""
        query_embedding = self.encode_text(query)
        scores = np.dot(self.embeddings, query_embedding.T).flatten()
        
        top_indices = np.argsort(scores)[::-1][:top_k]
        results = []
        for idx in top_indices:
            results.append({
                'chunk': self.chunks[idx],
                'score': float(scores[idx])
            })
        return results

class SparseRetriever:
    """稀疏检索器 - 使用BM25"""
    def __init__(self):
        self.bm25 = None
        self.chunks = []
        self.tokenized_corpus = []
        
    def build_index(self, chunks: List[Dict]):
        """构建BM25索引"""
        print("Building sparse (BM25) index...")
        self.chunks = chunks
        
        # 分词
        corpus = [chunk['content'] for chunk in chunks]
        self.tokenized_corpus = [list(jieba.cut(doc)) for doc in corpus]
        
        # 构建BM25索引
        self.bm25 = BM25Okapi(self.tokenized_corpus)
        print(f"BM25 index built with {len(self.chunks)} chunks")
    
    def search(self, query: str, top_k: int = 10) -> List[Dict]:
        """BM25搜索"""
        tokenized_query = list(jieba.cut(query))
        scores = self.bm25.get_scores(tokenized_query)
        
        top_indices = np.argsort(scores)[::-1][:top_k]
        results = []
        for idx in top_indices:
            results.append({
                'chunk': self.chunks[idx],
                'score': float(scores[idx])
            })
        return results

class Reranker:
    """重排序器 - 使用交叉编码器"""
    def __init__(self, model_name: str = "BAAI/bge-reranker-base"):
        print(f"Loading reranker model: {model_name}")
        self.model = CrossEncoder(model_name)
    
    def rerank(self, query: str, chunks: List[Dict], top_k: int = 5) -> List[Dict]:
        """重排序候选文档"""
        if not chunks:
            return []
        
        # 准备输入对
        pairs = [(query, chunk['content']) for chunk in chunks]
        
        # 计算相关性分数
        scores = self.model.predict(pairs)
        
        # 排序并返回top_k
        scored_chunks = list(zip(chunks, scores))
        scored_chunks.sort(key=lambda x: x[1], reverse=True)
        
        results = []
        for chunk, score in scored_chunks[:top_k]:
            results.append({
                'chunk': chunk,
                'score': float(score)
            })
        return results

class HybridRAG:
    """混合检索RAG系统"""
    def __init__(self, llm_model_name: str = "Qwen/Qwen3-8B"):
        # 初始化检索器
        self.dense_retriever = DenseRetriever()
        self.sparse_retriever = SparseRetriever()
        self.reranker = Reranker()
        
        # 初始化LLM
        print(f"Loading LLM: {llm_model_name}")
        self.tokenizer = AutoTokenizer.from_pretrained(llm_model_name)
        self.llm_device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.llm_model = AutoModelForCausalLM.from_pretrained(
            llm_model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def build_index(self, chunks: List[Dict]):
        """构建所有索引"""
        print("Building hybrid index...")
        self.dense_retriever.build_index(chunks)
        self.sparse_retriever.build_index(chunks)
        print("Hybrid index built successfully!")
    
    def reciprocal_rank_fusion(self, results_list: List[List[Dict]], k: int = 60) -> List[Dict]:
        """倒数排名融合 (RRF)"""
        chunk_scores = {}
        
        for results in results_list:
            for rank, result in enumerate(results):
                chunk_id = result['chunk']['id']
                if chunk_id not in chunk_scores:
                    chunk_scores[chunk_id] = {'chunk': result['chunk'], 'score': 0}
                chunk_scores[chunk_id]['score'] += 1 / (k + rank + 1)
        
        # 按分数排序
        fused_results = list(chunk_scores.values())
        fused_results.sort(key=lambda x: x['score'], reverse=True)
        
        return fused_results
    
    def query(self, question: str, top_k_retrieve: int = 20, top_k_rerank: int = 5) -> Dict[str, Any]:
        """执行混合检索查询"""
        print(f"Processing question: {question}")
        
        # --- 1. 混合检索 ---
        print("Step 1: Hybrid retrieval...")
        dense_results = self.dense_retriever.search(question, top_k=top_k_retrieve)
        sparse_results = self.sparse_retriever.search(question, top_k=top_k_retrieve)

        # --- 2. 结果融合 (Fusion) ---
        fused_results = self.reciprocal_rank_fusion([dense_results, sparse_results])
        candidate_chunks = [res['chunk'] for res in fused_results[:top_k_retrieve]]

        # --- 3. 重排序 (Re-ranking) ---
        print("Step 2: Re-ranking candidates...")
        reranked_chunks_with_scores = self.reranker.rerank(question, candidate_chunks, top_k=top_k_rerank)
        final_chunks = [res['chunk'] for res in reranked_chunks_with_scores]
        
        # --- 4. 构建Prompt并生成答案 ---
        print("Step 3: Generating answer with LLM...")
        context = "\n\n---\n\n".join([f"来源: {c['metadata']['file_name']}, 页码: {c['metadata']['page']}\n\n内容: {c['content']}" for c in final_chunks])
        
        # ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼【优化后的Prompt - 避免思考过程】▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
        prompt = (
            "你是一名专业的金融分析师。请基于提供的上下文信息回答问题。\n"
            "重要要求：\n"
            "1. 只能使用下面提供的上下文信息，不能使用任何外部知识\n"
            "2. 直接输出JSON格式的回答，不要输出任何思考过程、解释或其他文字\n"
            "3. 必须严格按照以下JSON格式输出：\n\n"
            "{\n"
            '  "answer": "基于上下文的简洁准确答案",\n'
            '  "filename": "主要信息来源文件名",\n'
            '  "page": "主要信息来源页码"\n'
            "}\n\n"
            "如果上下文信息不足以回答问题，则输出：\n"
            "{\n"
            '  "answer": "根据提供的资料无法回答该问题",\n'
            '  "filename": "",\n'
            '  "page": ""\n'
            "}\n\n"
            "--- 上下文信息 ---\n"
            f"{context}\n\n"
            "--- 问题 ---\n"
            f"{question}\n\n"
            "请直接输出JSON格式的回答："
        )
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲【优化后的Prompt - 避免思考过程】▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        messages = [{"role": "user", "content": prompt}]
        
        # Qwen3的模板要求，不加system prompt效果可能更好
        # messages = [
        #     {"role": "system", "content": "你是一名专业的金融分析助手。"},
        #     {"role": "user", "content": prompt}
        # ]
        
        text_prompt = self.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True)
        
        model_inputs = self.tokenizer([text_prompt], return_tensors="pt").to(self.llm_device)

        with torch.no_grad():
            generated_ids = self.llm_model.generate(
                model_inputs.input_ids, max_new_tokens=1024, pad_token_id=self.tokenizer.eos_token_id)
        
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
        ]
        
        response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        
        return {
            'question': question,
            'answer': response,
            'retrieval_chunks': final_chunks
        }

def load_chunks(file_path: str) -> List[Dict]:
    """加载文档chunks"""
    print(f"Loading chunks from {file_path}")
    with open(file_path, 'r', encoding='utf-8') as f:
        chunks = json.load(f)
    print(f"Loaded {len(chunks)} chunks")
    return chunks

def main():
    """主函数"""
    # 设置为None则处理全部测试集，设置为小数字进行测试
    TEST_SAMPLE_NUM = 3  # 先测试3个问题
    
    # 读取测试集
    test_path = os.path.join(os.path.dirname(__file__), 'datas/test.json')
    with open(test_path, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    if TEST_SAMPLE_NUM:
        test_data = test_data[:TEST_SAMPLE_NUM]
        print(f"Testing with {len(test_data)} samples")
    
    # 加载文档chunks
    chunks_path = "all_pdf_page_chunks.json"
    chunks = load_chunks(chunks_path)
    
    # 初始化RAG系统
    rag = HybridRAG()
    rag.build_index(chunks)
    
    # 处理测试数据
    results = []
    raw_results = []
    
    for i, item in enumerate(test_data):
        question = item['question']
        print(f"\n{'='*50}")
        print(f"Question {i+1}/{len(test_data)}: {question}")
        print('='*50)
        
        try:
            result = rag.query(question)
            
            # 解析JSON回答
            answer_text = result['answer'].strip()
            
            # 尝试解析JSON
            try:
                # 如果回答包含```json，提取其中的JSON部分
                if "```json" in answer_text:
                    start = answer_text.find("```json") + 7
                    end = answer_text.find("```", start)
                    if end != -1:
                        answer_text = answer_text[start:end].strip()
                
                # 如果回答以{开头，尝试直接解析
                if answer_text.startswith('{'):
                    answer_json = json.loads(answer_text)
                    answer = answer_json.get('answer', '')
                    filename = answer_json.get('filename', '')
                    page = answer_json.get('page', '')
                else:
                    # 如果不是JSON格式，直接使用原始回答
                    answer = answer_text
                    filename = ""
                    page = ""
                    
            except json.JSONDecodeError:
                # JSON解析失败，使用原始回答
                answer = answer_text
                filename = ""
                page = ""
            
            # 保存结果
            results.append({
                "question": question,
                "answer": answer,
                "filename": filename,
                "page": page
            })
            
            # 保存原始结果（用于调试）
            raw_results.append([i, result])
            
            print(f"Answer: {answer}")
            print(f"Source: {filename}, Page: {page}")
            
        except Exception as e:
            print(f"Error processing question {i+1}: {e}")
            results.append({
                "question": question,
                "answer": "",
                "filename": "",
                "page": ""
            })
    
    # 保存结果
    with open("rag_top1_pred.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    with open("rag_top1_pred_raw.json", "w", encoding="utf-8") as f:
        json.dump(raw_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Processing completed! Results saved to rag_top1_pred.json")
    print(f"📊 Processed {len(results)} questions")

if __name__ == "__main__":
    main()
