#!/usr/bin/env python3
"""
调试路径问题
"""

import os
import json
from pathlib import Path

def main():
    print("🔍 调试图片路径问题\n")
    
    # 检查一个page_content文件中的图片路径
    page_content_file = "data_base_json_page_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页_page_content.json"
    
    if not os.path.exists(page_content_file):
        print(f"❌ 文件不存在: {page_content_file}")
        return
    
    with open(page_content_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 找第一个包含图片的页面
    for page_idx, content in data.items():
        if 'images/' in content:
            print(f"📄 检查页面 {page_idx}")
            
            # 提取图片路径
            import re
            img_paths = re.findall(r'images/([^)]+\.jpg)', content)
            if img_paths:
                img_filename = img_paths[0]
                print(f"🖼️  图片文件名: {img_filename}")
                
                # 检查可能的路径
                possible_paths = [
                    f"images/{img_filename}",  # 相对于当前目录
                    f"data_base_json_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/auto/images/{img_filename}",
                    f"data_base_json_page_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/images/{img_filename}",
                ]
                
                print("📁 路径检查:")
                for path in possible_paths:
                    exists = os.path.exists(path)
                    print(f"  {'✅' if exists else '❌'} {path}")
                
                break
    
    print("\\n💡 问题分析:")
    print("Vision调用时，工作目录可能不对，导致相对路径images/xxx.jpg找不到实际文件")

if __name__ == "__main__":
    main()