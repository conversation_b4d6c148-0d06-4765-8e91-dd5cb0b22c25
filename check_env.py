# 文件名: check_env.py
try:
    import torch
    print(f"✅ PyTorch (torch)           : OK, version {torch.__version__}")
    print(f"   - GPU可用?              : {torch.cuda.is_available()}")

    import torchvision
    print(f"✅ TorchVision (torchvision)   : OK, version {torchvision.__version__}")

    import torchaudio
    print(f"✅ TorchAudio (torchaudio)     : OK, version {torchaudio.__version__}")

    import transformers
    print(f"✅ Transformers              : OK, version {transformers.__version__}")

    import sentence_transformers
    print(f"✅ Sentence-Transformers     : OK")

    import accelerate
    print(f"✅ Accelerate                : OK, version {accelerate.__version__}")

    import bitsandbytes
    print(f"✅ BitsAndBytes              : OK")

    print("\n🎉 所有核心库均已成功安装！")

except ImportError as e:
    print(f"\n❌ 安装失败，找不到模块: {e}")