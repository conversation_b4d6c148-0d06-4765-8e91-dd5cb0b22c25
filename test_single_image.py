#!/usr/bin/env python3
"""
测试单张图片的Vision处理
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()
sys.path.append(os.path.dirname(__file__))

from mineru_pipeline_all import item_to_markdown

def main():
    print("🧪 单张图片Vision处理测试\n")
    
    # 找一个真实的图片文件
    img_path = "data_base_json_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/auto/images/ba5b023d9cb0279b87f166d6356e4e5e3ef7e0759cc0c952f711431e6435cdef.jpg"
    
    print(f"📂 测试图片路径: {img_path}")
    print(f"✓ 文件存在: {os.path.exists(img_path)}")
    print(f"📏 文件大小: {os.path.getsize(img_path) if os.path.exists(img_path) else 0} bytes")
    
    # 测试1: 无caption的情况
    print("\n=== 测试1: 无caption ===")
    test_item1 = {
        "type": "image",
        "img_path": img_path,
        "image_caption": [],  # 空caption
        "page_idx": 0
    }
    
    try:
        result1 = item_to_markdown(test_item1, enable_image_caption=True)
        print(f"✓ 处理成功")
        print(f"📝 结果长度: {len(result1)}")
        print(f"🤖 包含AI描述: {'AI描述' in str(test_item1.get('image_caption', []))}")
        if 'AI描述' in str(test_item1.get('image_caption', [])):
            print(f"🎉 Caption: {test_item1['image_caption']}")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: 有简单caption的情况
    print("\n=== 测试2: 简单caption ===")
    test_item2 = {
        "type": "image", 
        "img_path": img_path,
        "image_caption": ["图表"],  # 简单caption
        "page_idx": 0
    }
    
    try:
        result2 = item_to_markdown(test_item2, enable_image_caption=True)
        print(f"✓ 处理成功")
        print(f"📝 结果长度: {len(result2)}")
        print(f"🤖 包含AI描述: {'AI描述' in str(test_item2.get('image_caption', []))}")
        if 'AI描述' in str(test_item2.get('image_caption', [])):
            print(f"🎉 Caption: {test_item2['image_caption']}")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()