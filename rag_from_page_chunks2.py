import json
import os
from typing import List, Dict, Any
from tqdm import tqdm
import sys
import torch
import jieba
from transformers import AutoModelForCausalLM, AutoTokenizer
from sentence_transformers import SentenceTransformer
from sentence_transformers.cross_encoder import CrossEncoder
from rank_bm25 import BM25Okapi

# -------------------------------------------------------------------
#  核心功能类定义 (Classes Definition)
# -------------------------------------------------------------------

class PageChunkLoader:
    """负责从JSON文件加载处理好的文档块"""
    def __init__(self, json_path: str):
        self.json_path = json_path
    def load_chunks(self) -> List[Dict[str, Any]]:
        with open(self.json_path, 'r', encoding='utf-8') as f:
            return json.load(f)

class EmbeddingModel:
    """使用本地bge-m3模型进行向量化"""
    def __init__(self, model_name: str = 'Qwen/Qwen3-Embedding-4B', batch_size: int = 8):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Embedding model ('{model_name}') is running on: {self.device}")
        self.model = SentenceTransformer(model_name, device=self.device)
        self.batch_size = batch_size
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        print(f"Embedding {len(texts)} texts using local model...")
        return self.model.encode(
            texts, batch_size=self.batch_size, convert_to_numpy=True, show_progress_bar=True
        ).tolist()
    def embed_text(self, text: str) -> List[float]:
        return self.embed_texts([text])[0]

class SimpleVectorStore:
    """简易的内存向量存储与暴力搜索"""
    def __init__(self):
        self.embeddings = []
        self.chunks = []
    def add_chunks(self, chunks: List[Dict[str, Any]], embeddings: List[List[float]]):
        self.chunks.extend(chunks)
        self.embeddings.extend(embeddings)
    def search(self, query_embedding: List[float], top_k: int = 10) -> List[Dict[str, Any]]:
        from numpy import dot
        from numpy.linalg import norm
        import numpy as np
        if not self.embeddings: return []
        emb_matrix = np.array(self.embeddings)
        query_emb = np.array(query_embedding)
        sims = emb_matrix @ query_emb / (norm(emb_matrix, axis=1) * norm(query_emb) + 1e-8)
        idxs = sims.argsort()[::-1][:top_k]
        return [{'chunk': self.chunks[i], 'score': sims[i]} for i in idxs]

class SparseRetriever:
    """使用BM25进行稀疏/关键词检索"""
    def __init__(self, chunks: List[Dict[str, Any]]):
        self.chunks = {c['id']: c for c in chunks}
        corpus = [c['content'] for c in chunks]
        print("Initializing BM25 index...")
        tokenized_corpus = [list(jieba.cut(doc)) for doc in tqdm(corpus, desc="Tokenizing corpus for BM25")]
        self.bm25 = BM25Okapi(tokenized_corpus)
        self.doc_ids = [c['id'] for c in chunks]
        print("BM25 index initialized.")
    def search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        tokenized_query = list(jieba.cut(query))
        doc_scores = self.bm25.get_scores(tokenized_query)
        top_n_indices = doc_scores.argsort()[::-1][:top_k]
        return [{'chunk': self.chunks[self.doc_ids[i]], 'score': doc_scores[i]} for i in top_n_indices]

class ReRanker:
    """使用交叉编码器对检索结果进行重排序/精排"""
    def __init__(self, model_name: str = 'BAAI/bge-reranker-large'):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Re-ranker model ('{model_name}') is running on: {self.device}")
        self.model = CrossEncoder(model_name, max_length=512, device=self.device)
        print("Re-ranker model loaded successfully.")
    def rerank(self, query: str, chunks: List[Dict[str, Any]], top_k: int = 3) -> List[Dict[str, Any]]:
        if not chunks: return []
        pairs = [[query, chunk['content']] for chunk in chunks]
        with torch.no_grad():
            scores = self.model.predict(pairs, show_progress_bar=False)
        ranked_results = [{'chunk': chunks[i], 'score': scores[i]} for i in range(len(scores))]
        ranked_results = sorted(ranked_results, key=lambda x: x['score'], reverse=True)
        return ranked_results[:top_k]

class AdvancedRAG:
    """整合所有组件的RAG系统总控制器"""
    def __init__(self, chunk_json_path: str):
        self.loader = PageChunkLoader(chunk_json_path)
        self.chunks = self.loader.load_chunks()

        # 初始化各个组件
        self.embedding_model = EmbeddingModel()
        self.vector_store = SimpleVectorStore()
        self.sparse_retriever = SparseRetriever(self.chunks)
        self.reranker = ReRanker()
        
        # 初始化大语言模型 (LLM)
        self.llm_device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Text generation model will run on: {self.llm_device}")
        self.llm_model_name = 'Qwen/Qwen3-8B'
        self.tokenizer = AutoTokenizer.from_pretrained(self.llm_model_name, trust_remote_code=True)
        print("Loading text generation model...")
        self.llm_model = AutoModelForCausalLM.from_pretrained(
            self.llm_model_name, torch_dtype="auto", device_map="auto",
            trust_remote_code=True, load_in_4bit=True)
        self.llm_model.eval()
        print("Text generation model loaded successfully!")

    def setup(self):
        """构建向量数据库和BM25索引"""
        print("Generating embeddings for vector store...")
        embeddings = self.embedding_model.embed_texts([c['content'] for c in self.chunks])
        self.vector_store.add_chunks(self.chunks, embeddings)
        print("RAG setup complete! All models and indexes are ready.")

    def reciprocal_rank_fusion(self, search_results_list, k=60):
        """倒数排名融合算法，用于合并多路检索结果"""
        fused_scores = {}
        # 使用set来跟踪所有出现过的文档ID
        all_doc_ids = set()
        for doc_results in search_results_list:
            for doc in doc_results:
                all_doc_ids.add(doc['chunk']['id'])

        # 初始化所有文档的分数
        for doc_id in all_doc_ids:
            fused_scores[doc_id] = 0
        
        # 计算RRF分数
        for doc_results in search_results_list:
            for i, doc in enumerate(doc_results):
                fused_scores[doc['chunk']['id']] += 1 / (k + i + 1)
        
        reranked_results = [{'chunk': self.sparse_retriever.chunks[doc_id], 'score': score} 
                            for doc_id, score in fused_scores.items()]
        return sorted(reranked_results, key=lambda x: x['score'], reverse=True)

    def generate_answer(self, question: str, top_k_retrieve: int = 20, top_k_rerank: int = 5) -> Dict[str, Any]:
        
        # --- 1. 混合检索 (Hybrid Search) ---
        print(f"\nStep 1: Hybrid searching for: '{question[:30]}...'")
        query_embedding = self.embedding_model.embed_text(question)
        dense_results = self.vector_store.search(query_embedding, top_k=top_k_retrieve)
        sparse_results = self.sparse_retriever.search(question, top_k=top_k_retrieve)

        # --- 2. 结果融合 (Fusion) ---
        fused_results = self.reciprocal_rank_fusion([dense_results, sparse_results])
        candidate_chunks = [res['chunk'] for res in fused_results[:top_k_retrieve]]

        # --- 3. 重排序 (Re-ranking) ---
        print("Step 2: Re-ranking candidates...")
        reranked_chunks_with_scores = self.reranker.rerank(question, candidate_chunks, top_k=top_k_rerank)
        final_chunks = [res['chunk'] for res in reranked_chunks_with_scores]
        
        # --- 4. 构建Prompt并生成答案 ---
        print("Step 3: Generating answer with LLM...")
        context = "\n\n---\n\n".join([f"来源: {c['metadata']['file_name']}, 页码: {c['metadata']['page']}\n\n内容: {c['content']}" for c in final_chunks])
        
        # ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼【优化后的Prompt】▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
        prompt = (
            "你是一名严谨、专业的金融分析师。你的任务是作为一个基于公司财报的智能问答引擎。\n"
            "你必须严格、完全、只依赖下面提供的“上下文信息”来回答“用户问题”。禁止使用任何外部知识。\n"
            "上下文信息可能包含财报的段落、表格数据和图表描述，请综合分析这些信息。\n"
            "你的回答必须遵循以下JSON格式，不要输出任何其他多余的解释或文字。\n"
            "```json\n"
            "{\n"
            '  "answer": "在这里给出你基于上下文总结出的简洁、精确的答案。",\n'
            '  "filename": "在这里填写最主要信息来源的文件名",\n'
            '  "page": "在这里填写最主要信息来源的页码"\n'
            "}\n"
            "```\n"
            "如果上下文中没有足够信息来回答问题，请在answer字段中明确说明“根据提供的资料无法回答该问题”，并将filename和page字段留空。\n\n"
            "--- 下面是上下文信息 ---\n"
            f"{context}\n\n"
            "--- 根据以上信息，回答以下问题 ---\n"
            f"用户问题: {question}\n"
        )
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲【优化后的Prompt】▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        messages = [{"role": "user", "content": prompt}]
        
        # Qwen3的模板要求，不加system prompt效果可能更好
        # messages = [
        #     {"role": "system", "content": "你是一名专业的金融分析助手。"},
        #     {"role": "user", "content": prompt}
        # ]
        
        text_prompt = self.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True)
        
        model_inputs = self.tokenizer([text_prompt], return_tensors="pt").to(self.llm_device)

        with torch.no_grad():
            generated_ids = self.llm_model.generate(
                model_inputs.input_ids, max_new_tokens=1024, pad_token_id=self.tokenizer.eos_token_id)
        
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)]
        raw = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        
        # --- JSON解析逻辑 ---
        # 使用工具从可能包含多余文字的回复中提取出JSON部分
        sys.path.append(os.path.dirname(__file__))
        from extract_json_array import extract_json_array
        json_str = extract_json_array(raw, mode='objects')
        answer, filename, page = raw, "", "" # 默认值
        try:
            # 优先使用提取出的JSON
            if json_str:
                parsed_json = json.loads(json_str)
                j = parsed_json[0] if isinstance(parsed_json, list) and parsed_json else parsed_json
                if isinstance(j, dict):
                    answer = j.get('answer', raw)
                    filename = j.get('filename', '')
                    page = j.get('page', '')
            else: # 如果无法提取出JSON，则尝试从上下文中找到最相关的来源
                if final_chunks:
                    # 使用重排序后得分最高的chunk作为来源
                    most_relevant_chunk = final_chunks[0]
                    filename = most_relevant_chunk['metadata']['file_name']
                    page = most_relevant_chunk['metadata']['page']
        except Exception as e:
            print(f"Error parsing LLM output: {e}. Raw output: {raw}")
            if final_chunks:
                most_relevant_chunk = final_chunks[0]
                filename = most_relevant_chunk['metadata']['file_name']
                page = most_relevant_chunk['metadata']['page']

        return {
            "question": question,
            "answer": answer,
            "filename": filename,
            "page": str(page),
            "retrieval_chunks": final_chunks
        }


# -------------------------------------------------------------------
#  主程序执行入口 (Main Execution Block)
# -------------------------------------------------------------------
if __name__ == '__main__':
    # 确保已安装所需库: pip install jieba rank-bm25
    chunk_json_path = os.path.join(os.path.dirname(__file__), 'all_pdf_page_chunks.json')
    
    # 初始化RAG系统 (此时会加载所有模型到GPU, 请确保显存充足)
    rag = AdvancedRAG(chunk_json_path)
    
    # 构建向量数据库和BM25索引
    rag.setup()

    # 设置为None则处理全部测试集，设置为小数字进行测试
    TEST_SAMPLE_NUM = None  # 先测试3个问题

    # 读取测试集
    test_path = os.path.join(os.path.dirname(__file__), 'datas/test.json')
    if os.path.exists(test_path):
        with open(test_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        import concurrent.futures
        import random

        all_indices = list(range(len(test_data)))
        selected_indices = all_indices
        if TEST_SAMPLE_NUM is not None and TEST_SAMPLE_NUM > 0 and len(test_data) > TEST_SAMPLE_NUM:
            selected_indices = sorted(random.sample(all_indices, TEST_SAMPLE_NUM))

        def process_one(idx):
            item = test_data[idx]
            question = item['question']
            tqdm.write(f"[{all_indices.index(idx)+1}/{len(all_indices)}] 正在处理: {question[:30]}...")
            try:
                result = rag.generate_answer(question)
                return idx, result
            except Exception as e:
                tqdm.write(f"处理问题 {idx} 时发生致命错误: {e}")
                return idx, {"question": question, "answer": f"ERROR: {e}", "filename": "", "page": ""}

        results = []
        if selected_indices:
            # 本地GPU推理时, 并发数设为1, 避免显存溢出
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                # 使用 to_iterator 实时获取结果, 避免tqdm的并发问题
                future_to_idx = {executor.submit(process_one, idx): idx for idx in selected_indices}
                for future in tqdm(concurrent.futures.as_completed(future_to_idx), total=len(selected_indices), desc='批量生成'):
                    results.append(future.result())

        # 按原始索引排序结果
        results.sort(key=lambda x: x[0])

        # 保存原始结果
        raw_out_path = os.path.join(os.path.dirname(__file__), 'rag_top1_pred_raw.json')
        with open(raw_out_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f'已输出原始未过滤结果到: {raw_out_path}')

        # 整理成最终提交格式
        idx2result = {idx: {k: v for k, v in r.items() if k != 'retrieval_chunks'} for idx, r in results}
        filtered_results = []
        for idx, item in enumerate(test_data):
            if idx in idx2result:
                filtered_results.append(idx2result[idx])
            else: # 如果有题目未被处理 (理论上不应发生, 除非被抽样)
                filtered_results.append({ "question": item.get("question", ""), "answer": "", "filename": "", "page": ""})
        
        out_path = os.path.join(os.path.dirname(__file__), 'rag_top1_pred.json')
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_results, f, ensure_ascii=False, indent=2)
        print(f'已输出结构化检索+大模型生成结果到: {out_path}')