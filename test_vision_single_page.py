#!/usr/bin/env python3
"""
测试单个页面的Vision处理
"""

import os
import json
from pathlib import Path
import asyncio
from collections import defaultdict
from dotenv import load_dotenv
load_dotenv()

# 复制并简化item_to_markdown函数
def test_item_to_markdown(item, enable_image_caption=True):
    """测试版本的item_to_markdown函数"""
    from image_utils.async_image_analysis import AsyncImageAnalysis
    
    vision_provider = "guiji" 
    vision_model = os.getenv("LOCAL_VISION_MODEL", "Pro/Qwen/Qwen2.5-VL-7B-Instruct")
    vision_api_key = os.getenv("LOCAL_API_KEY")
    vision_base_url = os.getenv("LOCAL_BASE_URL")
    
    if enable_image_caption:
        print(f"Vision配置: provider={vision_provider}, model={vision_model}")
        print(f"API检查: key存在={bool(vision_api_key)}, base_url={vision_base_url}")
    
    if item['type'] == 'image':
        captions = item.get('image_caption', [])
        caption = captions[0] if captions else ''
        img_path = item.get('img_path', '')
        
        # 修复路径问题
        absolute_img_path = img_path
        if img_path and img_path.startswith('images/'):
            base_dir = Path.cwd()
            content_dirs = list(base_dir.glob('data_base_json_content/*/*/auto'))
            for content_dir in content_dirs:
                potential_path = content_dir / img_path
                if potential_path.exists():
                    absolute_img_path = str(potential_path)
                    break
        
        should_analyze = enable_image_caption and img_path and os.path.exists(absolute_img_path)
        has_basic_caption = caption and len(caption) < 50
        
        if should_analyze and (not caption or has_basic_caption):
            try:
                print(f"正在分析图片: {absolute_img_path}")
                print(f"原始路径: {img_path}")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def get_enhanced_caption():
                    async with AsyncImageAnalysis(
                        provider=vision_provider,
                        api_key=vision_api_key,
                        base_url=vision_base_url,
                        vision_model=vision_model
                    ) as analyzer:
                        result = await analyzer.analyze_image(local_image_path=absolute_img_path)
                        enhanced_description = result.get('description', '')
                        title = result.get('title', '')
                        return enhanced_description if enhanced_description else title
                
                enhanced_caption = loop.run_until_complete(get_enhanced_caption())
                loop.close()
                
                if enhanced_caption:
                    if caption:
                        item['image_caption'] = [caption, f"AI描述: {enhanced_caption}"]
                        print(f"图片caption已增强: {enhanced_caption[:100]}...")
                    else:
                        item['image_caption'] = [enhanced_caption]
                        print(f"图片caption已生成: {enhanced_caption[:100]}...")
                    caption = item['image_caption'][0]
                        
            except Exception as e:
                print(f"图片解释失败: {absolute_img_path}")
                print(f"原始路径: {img_path}")
                print(f"错误详情: {str(e)}")
                
        md = f"![{caption}]({img_path})\n"
        return md + "\n"
    
    return "测试完成\n"

def main():
    # 加载一个有图片的content_list.json
    json_path = "data_base_json_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/auto/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页_content_list.json"
    
    if not os.path.exists(json_path):
        print(f"❌ 找不到测试文件: {json_path}")
        return
    
    print(f"📖 加载测试文件: {json_path}")
    
    with open(json_path, 'r', encoding='utf-8') as f:
        content_list = json.load(f)
    
    # 找第一个图片项目
    for item in content_list:
        if item.get('type') == 'image' and item.get('img_path'):
            print(f"🖼️  找到图片项目: {item.get('img_path')}")
            result = test_item_to_markdown(item, enable_image_caption=True)
            print(f"✅ 处理结果: {result[:200]}...")
            break
    else:
        print("❌ 没有找到图片项目")

if __name__ == "__main__":
    main()