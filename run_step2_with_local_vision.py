#!/usr/bin/env python3
"""
使用本地Ollama qwen2.5vl模型重新运行步骤2
比API调用快3-5倍！
"""

import os
import json
import base64
import requests
from pathlib import Path
from collections import defaultdict

def image_to_base64(image_path):
    """将图片转换为base64编码"""
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode('utf-8')

def analyze_image_with_ollama(image_path):
    """使用本地Ollama分析图片"""
    try:
        # 转换图片为base64
        base64_image = image_to_base64(image_path)
        
        # 调用Ollama API
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "qwen2.5vl:7b",
                "prompt": "请详细描述这张图片中的内容，包括图表类型、数据趋势、关键信息等。如果是图表，请说明具体的数据和变化趋势。",
                "images": [base64_image],
                "stream": False
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '')
        else:
            print(f"Ollama API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Ollama分析失败: {str(e)}")
        return None

def group_by_page(content_list):
    pages = defaultdict(list)
    for item in content_list:
        page_idx = item.get('page_idx', 0)
        pages[page_idx].append(item)
    return dict(pages)

def item_to_markdown_with_local_vision(item, enable_image_caption=True):
    """
    使用本地Ollama进行图片分析的markdown转换
    """
    if item['type'] == 'text':
        level = item.get('text_level', 0)
        text = item.get('text', '')
        if level == 1:
            return f"# {text}\n\n"
        elif level == 2:
            return f"## {text}\n\n"
        else:
            return f"{text}\n\n"
            
    elif item['type'] == 'image':
        captions = item.get('image_caption', [])
        caption = captions[0] if captions else ''
        img_path = item.get('img_path', '')
        
        # 构建绝对路径
        absolute_img_path = img_path
        if img_path and img_path.startswith('images/'):
            base_dir = Path.cwd()
            content_dirs = list(base_dir.glob('data_base_json_content/*/*/auto'))
            for content_dir in content_dirs:
                potential_path = content_dir / img_path
                if potential_path.exists():
                    absolute_img_path = str(potential_path)
                    break
        
        should_analyze = enable_image_caption and img_path and os.path.exists(absolute_img_path)
        has_basic_caption = caption and len(caption) < 50
        
        if should_analyze and (not caption or has_basic_caption):
            print(f"🔍 本地分析图片: {img_path}")
            enhanced_caption = analyze_image_with_ollama(absolute_img_path)
            
            if enhanced_caption:
                if caption:
                    # 组合原标题和AI描述
                    final_caption = f"{caption} - {enhanced_caption}"
                    print(f"✅ 图片caption已增强: {enhanced_caption[:100]}...")
                else:
                    final_caption = enhanced_caption
                    print(f"✅ 图片caption已生成: {enhanced_caption[:100]}...")
                
                # 保存完整描述
                item['image_caption'] = [caption, f"AI描述: {enhanced_caption}"] if caption else [enhanced_caption]
                caption = final_caption
            else:
                print(f"❌ 图片分析失败: {img_path}")
        
        md = f"![{caption}]({img_path})\n"
        return md + "\n"
        
    elif item['type'] == 'table':
        captions = item.get('table_caption', [])
        caption = captions[0] if captions else ''
        table_html = item.get('table_body', '')
        img_path = item.get('img_path', '')
        md = ''
        if caption:
            md += f"**{caption}**\n"
        if img_path:
            md += f"![{caption}]({img_path})\n"
        md += f"{table_html}\n\n"
        return md
    else:
        return '\n'

def assemble_pages_to_markdown(pages):
    page_md = {}
    for page_idx in sorted(pages.keys()):
        md = ''
        for item in pages[page_idx]:
            md += item_to_markdown_with_local_vision(item, enable_image_caption=True)
        page_md[page_idx] = md
    return page_md

def process_single_pdf_with_local_vision(pdf_dir, output_dir):
    """使用本地Vision处理单个PDF"""
    file_name = pdf_dir.name
    json_path = pdf_dir / 'auto' / f'{file_name}_content_list.json'
    
    if not json_path.exists():
        sub_dir = pdf_dir / file_name
        json_path2 = sub_dir / 'auto' / f'{file_name}_content_list.json'
        if json_path2.exists():
            json_path = json_path2
        else:
            print(f"❌ 未找到: {json_path}")
            return False
    
    try:
        print(f"🔄 处理: {file_name}")
        
        with open(json_path, 'r', encoding='utf-8') as f:
            content_list = json.load(f)
        
        pages = group_by_page(content_list)
        page_md = assemble_pages_to_markdown(pages)
        
        output_pdf_dir = output_dir / file_name
        output_pdf_dir.mkdir(parents=True, exist_ok=True)
        output_json_path = output_pdf_dir / f'{file_name}_page_content.json'
        
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(page_md, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已输出: {output_json_path}")
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_name}: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("🚀 使用本地Ollama qwen2.5vl:7b 重新运行步骤2")
    print("🏃 比API调用快3-5倍！")
    print("=" * 60)
    
    # 检查Ollama是否运行
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama未运行，请先启动: ollama serve")
            return
        print("✅ Ollama服务正常")
    except:
        print("❌ 无法连接Ollama，请确保服务已启动")
        return
    
    base_dir = Path(__file__).parent
    content_dir = base_dir / 'data_base_json_content'
    page_dir = base_dir / 'data_base_json_page_content'
    
    if not content_dir.exists():
        print(f"❌ 输入目录不存在: {content_dir}")
        return
    
    # 获取所有PDF目录  
    pdf_dirs = [d for d in content_dir.iterdir() if d.is_dir()]
    total_pdfs = len(pdf_dirs)
    
    print(f"📊 总共需要处理: {total_pdfs} 个PDF")
    print(f"🎯 输出目录: {page_dir}")
    print()
    
    success_count = 0
    import time
    start_time = time.time()
    
    for i, pdf_dir in enumerate(pdf_dirs, 1):
        print(f"[{i}/{total_pdfs}] ", end="")
        if process_single_pdf_with_local_vision(pdf_dir, page_dir):
            success_count += 1
        
        # 每处理5个显示进度和预估时间
        if i % 5 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (total_pdfs - i) * avg_time
            print(f"\n📈 进度: {i}/{total_pdfs} ({i/total_pdfs*100:.1f}%) | 成功: {success_count}")
            print(f"⏱️  已耗时: {elapsed/60:.1f}分钟 | 预计剩余: {remaining/60:.1f}分钟")
    
    total_time = time.time() - start_time
    print("\n" + "=" * 60)
    print(f"🎉 本地Vision处理完成！")
    print(f"📊 总数: {total_pdfs} | 成功: {success_count} | 失败: {total_pdfs - success_count}")
    print(f"⏱️  总耗时: {total_time/60:.1f}分钟 | 平均: {total_time/total_pdfs:.1f}秒/PDF")
    
    if success_count == total_pdfs:
        print("✅ 所有文件处理成功！现在运行步骤3合并文件...")
        
        # 自动运行步骤3
        from mineru_pipeline_all import process_page_content_to_chunks
        chunk_json_path = base_dir / 'all_pdf_page_chunks_with_local_vision.json'
        process_page_content_to_chunks(page_dir, chunk_json_path)
        print(f"🎯 最终文件: {chunk_json_path}")

if __name__ == '__main__':
    main()