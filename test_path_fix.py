#!/usr/bin/env python3
"""
测试路径修复是否有效
"""

import os
import json
from pathlib import Path

def test_path_resolution():
    print("🧪 测试路径解析修复")
    
    # 模拟相对路径（来自content_list.json中的数据）
    img_path = "images/95dd572bc017cda064447db18625635e7dea7791c0315f131a848f89b8f846c8.jpg"
    
    print(f"📂 原始相对路径: {img_path}")
    print(f"❌ 相对路径存在检查: {os.path.exists(img_path)}")
    
    # 应用修复逻辑
    absolute_img_path = img_path
    if img_path and img_path.startswith('images/'):
        # 从当前工作目录查找对应的content目录中的图片
        base_dir = Path.cwd()
        content_dirs = list(base_dir.glob('data_base_json_content/*/*/auto'))
        for content_dir in content_dirs:
            potential_path = content_dir / img_path
            if potential_path.exists():
                absolute_img_path = str(potential_path)
                break
    
    print(f"🔧 修复后绝对路径: {absolute_img_path}")
    print(f"✅ 绝对路径存在检查: {os.path.exists(absolute_img_path)}")
    
    if absolute_img_path != img_path:
        print("🎉 路径修复成功！")
        return True
    else:
        print("❌ 路径修复失败")
        return False

if __name__ == "__main__":
    test_path_resolution()