# import os
# from transformers import AutoModel, AutoTokenizer

# # 改用清华大学镜像（hf-mirror.com）
# # os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
# os.environ["HF_ENDPOINT"] = "https://hf.aliyun.com"
# # os.environ["HF_ENDPOINT"] = "https://huggingface.sjtu.edu.cn"

# # 加载模型（自动通过镜像下载）
# model = AutoModel.from_pretrained("BAAI/bge-reranker-large", trust_remote_code=True)
# tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-reranker-large")
# print("✅ 模型加载成功")

# 设置国内镜像源（清华）
import os
from transformers import AutoModel, AutoTokenizer

# 设置镜像
# os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HF_ENDPOINT"] = "https://hf.aliyun.com"


# 自动下载并加载
model = AutoModel.from_pretrained(
    # "BAAI/bge-large-zh-v1.5" ,
    "Qwen/Qwen3-Embedding-4B",
    trust_remote_code=True
)

# 测试推理
outputs = model.generate_embeddings(
    texts=["样例文本"],
    embedding_mode="sentence"  # 可选：paragraph|sentence|token
)