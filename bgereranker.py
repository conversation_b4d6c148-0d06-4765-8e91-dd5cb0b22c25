import os
from transformers import AutoModel, AutoTokenizer

# 改用清华大学镜像（hf-mirror.com）
# os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HF_ENDPOINT"] = "https://hf.aliyun.com"
# os.environ["HF_ENDPOINT"] = "https://huggingface.sjtu.edu.cn"

# 加载模型（自动通过镜像下载）
model = AutoModel.from_pretrained("BAAI/bge-reranker-large", trust_remote_code=True)
tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-reranker-large")
print("✅ 模型加载成功")