"""
创建标准化测试数据集
从原始806个样本中随机选择100个用于评估
"""

import json
import random
from pathlib import Path

def create_evaluation_dataset():
    """从原始测试集创建100个样本的评估数据集"""
    
    # 读取原始测试数据
    source_path = "datas/test.json"
    with open(source_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"原始测试集包含 {len(original_data)} 个问题")
    
    # 随机选择100个样本
    random.seed(42)  # 设置随机种子保证结果可复现
    selected_indices = sorted(random.sample(range(len(original_data)), 100))
    
    evaluation_dataset = []
    for i, idx in enumerate(selected_indices):
        item = original_data[idx]
        
        # 问题分类
        question = item.get("question", "")
        category = classify_question(question)
        requires_vision = requires_vision_analysis(question)
        
        # 创建标准化数据项
        eval_item = {
            "id": i,
            "original_index": idx,
            "question": question,
            "ground_truth_answer": item.get("answer", ""),
            "ground_truth_filename": item.get("filename", ""),
            "ground_truth_page": item.get("page", ""),
            "category": category,
            "requires_vision": requires_vision,
            "difficulty": "medium"  # 默认难度
        }
        
        evaluation_dataset.append(eval_item)
    
    # 保存评估数据集
    output_path = "evaluation_test_dataset.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(evaluation_dataset, f, ensure_ascii=False, indent=2)
    
    print(f"评估数据集已创建，共100个样本，保存至 {output_path}")
    
    # 统计信息
    categories = {}
    vision_count = 0
    
    for item in evaluation_dataset:
        cat = item["category"]
        categories[cat] = categories.get(cat, 0) + 1
        if item["requires_vision"]:
            vision_count += 1
    
    print("\n数据集统计信息：")
    print(f"问题类别分布: {categories}")
    print(f"需要视觉分析的问题: {vision_count}/100")
    
    return evaluation_dataset

def classify_question(question: str) -> str:
    """问题分类"""
    if any(keyword in question for keyword in ["营收", "收入", "利润", "财务", "业绩", "盈利"]):
        return "财务指标"
    elif any(keyword in question for keyword in ["技术", "研发", "产品", "项目", "BIM", "系统"]):
        return "业务技术"
    elif any(keyword in question for keyword in ["风险", "挑战", "问题", "困难"]):
        return "风险分析"
    elif any(keyword in question for keyword in ["图", "表", "数据", "统计", "显示"]):
        return "图表数据"
    elif any(keyword in question for keyword in ["战略", "规划", "目标", "发展"]):
        return "战略规划"
    else:
        return "综合信息"

def requires_vision_analysis(question: str) -> bool:
    """判断问题是否需要视觉分析"""
    vision_keywords = [
        "图", "表格", "图表", "图片", "显示", "数据表", 
        "统计图", "趋势图", "柱状图", "饼图", "曲线图",
        "示意图", "结构图", "流程图"
    ]
    return any(keyword in question for keyword in vision_keywords)

if __name__ == "__main__":
    create_evaluation_dataset()