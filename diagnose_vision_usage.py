#!/usr/bin/env python3
"""
诊断Vision功能是否真正被使用的详细检查脚本
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

def check_environment():
    """检查环境配置"""
    print("=== 🔧 环境配置检查 ===")
    api_key = os.getenv("LOCAL_API_KEY")
    base_url = os.getenv("LOCAL_BASE_URL")
    vision_model = os.getenv("LOCAL_VISION_MODEL")
    
    print(f"✓ API Key: {'存在' if api_key else '❌ 缺失'}")
    print(f"✓ Base URL: {base_url}")
    print(f"✓ Vision Model: {vision_model}")
    return bool(api_key and base_url and vision_model)

def check_processed_data():
    """检查处理后的数据"""
    print("\n=== 📊 处理数据检查 ===")
    
    # 检查最终输出文件
    final_file = "all_pdf_page_chunks.json"
    if not os.path.exists(final_file):
        print(f"❌ 找不到最终输出文件: {final_file}")
        return False
    
    # 检查文件修改时间
    import time
    mod_time = os.path.getmtime(final_file)
    mod_time_str = time.ctime(mod_time)
    print(f"📅 最终文件修改时间: {mod_time_str}")
    
    # 检查文件内容
    with open(final_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📦 总数据块数量: {len(data)}")
    
    # 统计图片相关信息
    image_count = 0
    enhanced_caption_count = 0
    empty_caption_count = 0
    
    for chunk in data:
        content = chunk.get('content', '')
        if '![' in content:  # 包含图片的markdown
            image_count += 1
            if 'AI描述' in content:
                enhanced_caption_count += 1
            elif '![](' in content:  # 空caption
                empty_caption_count += 1
    
    print(f"🖼️  包含图片的块: {image_count}")
    print(f"🤖 AI增强描述: {enhanced_caption_count}")
    print(f"❌ 空描述图片: {empty_caption_count}")
    
    return image_count, enhanced_caption_count, empty_caption_count

def check_intermediate_files():
    """检查中间处理文件"""
    print("\n=== 📁 中间文件检查 ===")
    
    # 检查page_content文件
    page_content_dir = Path("data_base_json_page_content")
    if not page_content_dir.exists():
        print("❌ 中间处理目录不存在")
        return False
    
    # 找一个样本文件检查
    sample_files = list(page_content_dir.rglob("*_page_content.json"))
    if not sample_files:
        print("❌ 没有找到中间处理文件")
        return False
    
    sample_file = sample_files[0]
    print(f"🔍 检查样本文件: {sample_file.name}")
    
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否有AI描述
        content_str = str(data)
        has_ai_desc = 'AI描述' in content_str
        has_analysis_log = '正在分析图片' in content_str
        
        print(f"🤖 包含AI描述: {'是' if has_ai_desc else '❌ 否'}")
        print(f"📝 包含分析日志: {'是' if has_analysis_log else '❌ 否'}")
        
        return has_ai_desc
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def check_image_files():
    """检查图片文件是否存在"""
    print("\n=== 🖼️  图片文件检查 ===")
    
    image_dirs = list(Path("data_base_json_content").rglob("images"))
    if not image_dirs:
        print("❌ 没有找到图片目录")
        return False
    
    total_images = 0
    for img_dir in image_dirs[:3]:  # 检查前3个目录
        images = list(img_dir.glob("*.jpg"))
        total_images += len(images)
        print(f"📂 {img_dir.parent.name}: {len(images)}张图片")
        
        # 检查第一张图片的路径
        if images:
            sample_img = images[0]
            print(f"   📄 样本: {sample_img.name}")
            print(f"   ✓ 文件存在: {sample_img.exists()}")
    
    print(f"🖼️  总图片数量: {total_images}")
    return total_images > 0

def run_mini_test():
    """运行一个小规模测试"""
    print("\n=== 🧪 小规模测试 ===")
    
    try:
        from mineru_pipeline_all import item_to_markdown
        
        # 创建测试item
        test_item = {
            "type": "image",
            "img_path": "test_nonexistent.jpg",  # 故意使用不存在的文件
            "image_caption": ["测试标题"],
            "page_idx": 0
        }
        
        print("🔄 测试item_to_markdown函数...")
        result = item_to_markdown(test_item, enable_image_caption=True)
        print(f"✓ 函数执行成功，返回长度: {len(result)}")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🔍 Vision功能使用情况诊断报告\n")
    
    # 环境检查
    env_ok = check_environment()
    
    # 数据检查
    image_count, enhanced_count, empty_count = check_processed_data()
    
    # 中间文件检查
    intermediate_ok = check_intermediate_files()
    
    # 图片文件检查
    images_exist = check_image_files()
    
    # 功能测试
    function_ok = run_mini_test()
    
    # 总结诊断结果
    print("\n" + "="*50)
    print("🎯 诊断结果总结")
    print("="*50)
    
    if enhanced_count > 0:
        print("✅ Vision功能已正常使用")
        print(f"   - 成功处理 {enhanced_count} 张图片")
    elif empty_count > 0 and images_exist:
        print("⚠️  Vision功能未使用或失败")
        print(f"   - 发现 {empty_count} 张未处理的图片")
        print("   - 可能原因：")
        print("     1. API调用失败")
        print("     2. 图片路径问题") 
        print("     3. 模型名称错误")
        print("     4. 网络连接问题")
    else:
        print("❌ 无法确定Vision使用状态")
    
    # 建议
    print("\n💡 建议：")
    if enhanced_count == 0:
        print("1. 重新运行 mineru_pipeline_all.py 并观察控制台输出")
        print("2. 检查是否有'正在分析图片'的日志")
        print("3. 检查API消费记录")
        print("4. 运行单独的Vision测试脚本")

if __name__ == "__main__":
    main()