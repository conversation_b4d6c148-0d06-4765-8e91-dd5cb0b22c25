{"cells": [{"cell_type": "markdown", "id": "860c4566", "metadata": {}, "source": ["# 用于处理多模态RAG图文问答挑战赛训练集JSON文件，提取问题与答案构建QA对\n", "本notebook将演示如何将原始训练集转换为标准QA格式，便于后续微调。"]}, {"cell_type": "code", "execution_count": 1, "id": "b4b72abf", "metadata": {}, "outputs": [], "source": ["# 1. 导入所需库\n", "import json\n", "import os\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "c4f53530", "metadata": {}, "source": ["## 2. 加载JSON数据集\n", "读取多模态RAG图文问答挑战赛训练集.json文件，解析为Python对象。"]}, {"cell_type": "code", "execution_count": 2, "id": "bb6e7b38", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["样本数量: 118\n", "示例样本: {'filename': '联邦制药-港股公司研究报告-创新突破三靶点战略联姻诺和诺德-25071225页.pdf', 'page': 4, 'question': '根据联邦制药（03933.HK）的公司发展历程，请简述其在2023年的重大产品临床进展。', 'answer': '根据联邦制药（03933.HK）的公司发展历程，2023年的重大产品临床进展如下：\\n\\n- **重磅产品临床**：\\n  - UBT251多个适应症进入临床阶段；\\n  - 司美格鲁肽注射液体重管理适应症获临床受理；\\n  - 利拉鲁肽注射液BLA；德谷胰岛素利拉鲁肽注射液获批临床。'}\n"]}], "source": ["# 2. 加载JSON数据集\n", "data_path = \"spark_multi_rag/datas/多模态RAG图文问答挑战赛训练集.json\"\n", "with open(data_path, \"r\", encoding=\"utf-8\") as f:\n", "    data = json.load(f)\n", "print(f\"样本数量: {len(data)}\")\n", "print(\"示例样本:\", data[0])"]}, {"cell_type": "markdown", "id": "1709f785", "metadata": {}, "source": ["## 3. 数据结构与字段解析\n", "分析每条数据的字段结构，重点关注filename、page、question、answer等字段。"]}, {"cell_type": "code", "execution_count": 3, "id": "45e36816", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["字段列表: ['filename', 'page', 'question', 'answer']\n", "filename: 联邦制药-港股公司研究报告-创新突破三靶点战略联姻诺和诺德-25071225页.pdf\n", "page: 4\n", "question: 根据联邦制药（03933.HK）的公司发展历程，请简述其在2023年的重大产品临床进展。\n", "answer: 根据联邦制药（03933.HK）的公司发展历程，2023年的重大产品临床进展如下：\n", "\n", "- **重磅产品临床**：\n", "  - UBT251多个适应症进入临床阶段；\n", "  - 司美格鲁肽注射液体重管理适应症获临床受理；\n", "  - 利拉鲁肽注射液BLA；德谷胰岛素利拉鲁肽注射液获批临床。\n"]}], "source": ["# 3. 数据结构与字段解析\n", "sample = data[0]\n", "print(\"字段列表:\", list(sample.keys()))\n", "for k, v in sample.items():\n", "    print(f\"{k}: {v}\")"]}, {"cell_type": "markdown", "id": "edf8a438", "metadata": {}, "source": ["## 4. 构建QA对列表\n", "遍历数据集，将每条数据的question和answer字段提取出来，构建QA对列表。指令字段统一设置为“你是一名专业的财报数据问答助手”。"]}, {"cell_type": "code", "execution_count": 4, "id": "aab594f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QA样例: {'instruction': '你是一名专业的财报数据问答助手', 'input': '根据联邦制药（03933.HK）的公司发展历程，请简述其在2023年的重大产品临床进展。', 'output': '根据联邦制药（03933.HK）的公司发展历程，2023年的重大产品临床进展如下：\\n\\n- **重磅产品临床**：\\n  - UBT251多个适应症进入临床阶段；\\n  - 司美格鲁肽注射液体重管理适应症获临床受理；\\n  - 利拉鲁肽注射液BLA；德谷胰岛素利拉鲁肽注射液获批临床。'}\n", "总QA数量: 118\n"]}], "source": ["# 4. 构建QA对列表\n", "qa_list = []\n", "for item in data:\n", "    qa = {\n", "        \"instruction\": \"你是一名专业的财报数据问答助手\",\n", "        \"input\": item.get(\"question\", \"\"),\n", "        \"output\": item.get(\"answer\", \"\")\n", "    }\n", "    qa_list.append(qa)\n", "print(\"QA样例:\", qa_list[0])\n", "print(f\"总QA数量: {len(qa_list)}\")"]}, {"cell_type": "markdown", "id": "0ef5f6cf", "metadata": {}, "source": ["## 5. 保存处理后的QA数据\n", "将处理后的QA对列表保存为新的JSON或CSV文件，便于后续模型训练或分析。"]}, {"cell_type": "code", "execution_count": 5, "id": "9a03b34c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已保存JSON文件: spark_multi_rag/datas/qa_train.json\n", "已保存CSV文件: spark_multi_rag/datas/qa_train.csv\n"]}], "source": ["# 5. 保存处理后的QA数据\n", "qa_json_path = \"spark_multi_rag/datas/qa_train.json\"\n", "with open(qa_json_path, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(qa_list, f, ensure_ascii=False, indent=2)\n", "print(f\"已保存JSON文件: {qa_json_path}\")\n", "\n", "# 可选：保存为CSV格式，便于表格分析\n", "qa_df = pd.DataFrame(qa_list)\n", "qa_csv_path = \"spark_multi_rag/datas/qa_train.csv\"\n", "qa_df.to_csv(qa_csv_path, index=False, encoding=\"utf-8\")\n", "print(f\"已保存CSV文件: {qa_csv_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "13474168", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "unsloth", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}