{"cells": [{"cell_type": "markdown", "id": "27817a3c", "metadata": {}, "source": ["# Qwen2.5-7B模型微调流程\n", "本notebook参考Qwen2_5_7B_Alpaca_fintune.ipynb，使用已处理好的QA数据进行微调。"]}, {"cell_type": "code", "execution_count": 1, "id": "c5fd4f07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/unsloth/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n"]}], "source": ["# 1. 安装和导入依赖库\n", "# 如果未安装可取消注释运行\n", "# !pip install unsloth bitsandbytes accelerate xformers==0.0.29.post3 peft trl sentencepiece protobuf datasets huggingface_hub hf_transfer\n", "from unsloth import FastLanguageModel\n", "import torch\n", "from datasets import load_dataset"]}, {"cell_type": "markdown", "id": "12469116", "metadata": {}, "source": ["## 2. 加载和配置Qwen2.5-7B模型\n", "设置最大序列长度、数据类型和量化方式，加载预训练模型和分词器。"]}, {"cell_type": "code", "execution_count": 2, "id": "06eb998d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth 2025.7.11: Fast Qwen2 patching. Transformers: 4.54.1.\n", "   \\\\   /|    NVIDIA RTX A6000. Num GPUs = 1. Max memory: 44.988 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 8.6. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 2/2 [00:04<00:00,  2.41s/it]\n", "\n"]}], "source": ["# 2. 加载和配置Qwen2.5-7B模型\n", "max_seq_length = 2048\n", "dtype = None\n", "load_in_4bit = True\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Qwen2.5-7B\",\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # 如需访问受限模型请填写token\n", ")"]}, {"cell_type": "markdown", "id": "c8863855", "metadata": {}, "source": ["## 3. 添加LoRA适配器\n", "为模型添加LoRA适配器，只微调部分参数以节省显存和加速训练。"]}, {"cell_type": "code", "execution_count": 3, "id": "fd8d0f99", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.7.11 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}], "source": ["# 3. 添加LoRA适配器\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16,\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                    \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    use_gradient_checkpointing = \"unsloth\",\n", "    random_state = 3407,\n", "    use_rslora = False,\n", "    loftq_config = None,\n", ")"]}, {"cell_type": "markdown", "id": "1b8e4b97", "metadata": {}, "source": ["## 4. 加载QA数据集\n", "加载已处理好的QA数据集（qa_train.json），用于微调。"]}, {"cell_type": "code", "execution_count": 4, "id": "12375445", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QA样本数: 118\n", "示例样本: {'instruction': '你是一名专业的财报数据问答助手', 'input': '根据联邦制药（03933.HK）的公司发展历程，请简述其在2023年的重大产品临床进展。', 'output': '根据联邦制药（03933.HK）的公司发展历程，2023年的重大产品临床进展如下：\\n\\n- **重磅产品临床**：\\n  - UBT251多个适应症进入临床阶段；\\n  - 司美格鲁肽注射液体重管理适应症获临床受理；\\n  - 利拉鲁肽注射液BLA；德谷胰岛素利拉鲁肽注射液获批临床。'}\n"]}], "source": ["# 4. 加载QA数据集\n", "qa_data_path = \"./datas/qa_train.json\"\n", "import json\n", "from datasets import Dataset\n", "with open(qa_data_path, \"r\", encoding=\"utf-8\") as f:\n", "    qa_data = json.load(f)\n", "dataset = Dataset.from_list(qa_data)\n", "print(f\"QA样本数: {len(dataset)}\")\n", "print(\"示例样本:\", dataset[0])"]}, {"cell_type": "markdown", "id": "cf237933", "metadata": {}, "source": ["## 5. 格式化数据并添加EOS标记\n", "定义格式化函数，将指令、输入和输出拼接为训练文本，并在末尾添加EOS标记。"]}, {"cell_type": "code", "execution_count": 5, "id": "97cdb79e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 118/118 [00:00<00:00, 19510.70 examples/s]\n", "\n"]}], "source": ["# 5. 格式化数据并添加EOS标记\n", "qa_prompt = \"\"\"以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\\n\\n### 指令:\\n{}\\n\\n### 输入:\\n{}\\n\\n### 回复:\\n{}\"\"\"\n", "EOS_TOKEN = tokenizer.eos_token\n", "def formatting_prompts_func(examples):\n", "    instructions = examples[\"instruction\"]\n", "    inputs = examples[\"input\"]\n", "    outputs = examples[\"output\"]\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        text = qa_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return {\"text\": texts}\n", "dataset = dataset.map(formatting_prompts_func, batched=True)"]}, {"cell_type": "markdown", "id": "e8a61abd", "metadata": {}, "source": ["## 6. 训练模型\n", "使用TRL的SFTTrainer进行微调，设置训练参数如批次大小、学习率、训练步数等。"]}, {"cell_type": "code", "execution_count": null, "id": "994672ba", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: Tokenizing [\"text\"] (num_proc=2): 100%|██████████| 118/118 [00:01<00:00, 107.92 examples/s]\n", "\n"]}], "source": ["# 6. 训练模型\n", "from trl import SFTConfig, SFTTrainer\n", "\n", "# SFTTrainer用于有监督微调（Supervised Fine-Tuning），可高效训练大模型\n", "trainer = SFTT<PERSON>er(\n", "    model = model,                      # 传入待微调的模型\n", "    tokenizer = tokenizer,              # 分词器，用于文本编码\n", "    train_dataset = dataset,            # 训练数据集，已格式化为\"text\"字段\n", "    dataset_text_field = \"text\",        # 指定训练文本字段名\n", "    max_seq_length = max_seq_length,    # 最大序列长度，影响显存和训练速度\n", "    packing = False,                    # 是否启用packing（将多个短文本拼接为一个长序列以加速训练），此处关闭\n", "    args = SFTConfig(                   # 训练参数配置\n", "        per_device_train_batch_size = 2,        # 每张GPU上的训练批次大小\n", "        gradient_accumulation_steps = 4,        # 梯度累积步数，等效于总batch_size=2*4=8\n", "        warmup_steps = 5,                       # 预热步数，前5步线性增加学习率\n", "        max_steps = 60,                         # 最大训练步数（可根据数据量和显存调整）\n", "        learning_rate = 2e-4,                   # 学习率\n", "        logging_steps = 1,                      # 日志打印频率，每步打印一次\n", "        optim = \"adamw_8bit\",                   # 优化器类型，采用8bit AdamW以节省显存\n", "        weight_decay = 0.01,                    # 权重衰减，防止过拟合\n", "        lr_scheduler_type = \"linear\",           # 学习率调度策略，线性下降\n", "        seed = 3407,                            # 随机种子，保证实验可复现\n", "        output_dir = \"spark_model_train_outputs\",# 输出目录，保存训练结果和模型\n", "        report_to = \"none\",                     # 日志上报平台（如wandb），此处关闭\n", "    ),\n", ")"]}, {"cell_type": "markdown", "id": "9a238891", "metadata": {}, "source": ["## 7. 显存信息监控\n", "通过torch.cuda获取和打印显卡显存使用情况，便于监控资源消耗。"]}, {"cell_type": "code", "execution_count": 7, "id": "8a23aeda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU型号: NVIDIA RTX A6000，最大显存: 44.988 GB\n", "已预留显存: 7.246 GB\n"]}], "source": ["# 7. 显存信息监控\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU型号: {gpu_stats.name}，最大显存: {max_memory} GB\")\n", "print(f\"已预留显存: {start_gpu_memory} GB\")"]}, {"cell_type": "markdown", "id": "ae82c3fa", "metadata": {}, "source": ["## 8. 启动训练并监控显存变化\n", "开始训练模型，并显示训练结果和显存变化。"]}, {"cell_type": "code", "execution_count": 8, "id": "e84cfa0b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 118 | Num Epochs = 4 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 40,370,176 of 7,655,986,688 (0.53% trained)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 02:40, Epoch 4/4]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>2.153800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>2.293200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>2.277500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>2.183100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>2.087200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>2.189800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.788600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.591500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.673200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.491400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.301200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.428400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.332400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.286000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.401500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.187700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.235300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.199400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.090200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>1.310900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>1.169900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.194700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>1.127300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.943600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.094400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.911900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.969400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.144700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.216600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.181400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.998400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>1.106300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.862800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>1.147700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>1.102000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.908800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>1.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.934300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>1.018400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.875200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>1.092300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.958100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.858900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>1.035400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>1.347600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>1.049900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.807800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.755700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>1.015500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.071900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.940000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.856700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.891300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.854600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.890900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.097600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>1.015600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.931400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.966600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.911200</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["训练耗时: 164.8592 秒\n", "训练耗时: 2.75 分钟\n", "训练期间峰值显存: 8.092 GB，占最大显存 17.987%\n", "LoRA训练显存: 0.846 GB，占最大显存 1.881%\n"]}], "source": ["# 8. 启动训练并监控显存变化\n", "trainer_stats = trainer.train()\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"训练耗时: {trainer_stats.metrics['train_runtime']} 秒\")\n", "print(f\"训练耗时: {round(trainer_stats.metrics['train_runtime']/60, 2)} 分钟\")\n", "print(f\"训练期间峰值显存: {used_memory} GB，占最大显存 {used_percentage}%\")\n", "print(f\"LoRA训练显存: {used_memory_for_lora} GB，占最大显存 {lora_percentage}%\")"]}, {"cell_type": "markdown", "id": "52c664e2", "metadata": {}, "source": ["## 9. 推理与保存微调后的模型\n", "训练完成后，进行简单推理验证，并保存LoRA适配器和分词器。"]}, {"cell_type": "code", "execution_count": 9, "id": "1fca6f97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["推理结果: ['以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\\n\\n### 指令:\\n你是一名专业的财报数据问答助手\\n\\n### 输入:\\n联邦制药2024年营业收入和净利润分别是多少？\\n\\n### 回复:\\n根据图片中的图表和文字内容，联邦制药2024年营业收入为100.1亿元，净利润为20.1亿元。<|endoftext|>']\n"]}], "source": ["# 9. 推理与保存微调后的模型\n", "FastLanguageModel.for_inference(model)  # 开启高效推理模式\n", "test_instruction = \"你是一名专业的财报数据问答助手\"\n", "test_input = \"联邦制药2024年营业收入和净利润分别是多少？\"\n", "test_prompt = qa_prompt.format(test_instruction, test_input, \"\")\n", "inputs = tokenizer([test_prompt], return_tensors=\"pt\").to(\"cuda\")\n", "outputs = model.generate(**inputs, max_new_tokens=64, use_cache=True)\n", "print(\"推理结果:\", tokenizer.batch_decode(outputs))\n"]}, {"cell_type": "code", "execution_count": null, "id": "b889f518", "metadata": {}, "outputs": [], "source": ["\n", "# 保存LoRA适配器和分词器到本地\n", "model.save_pretrained(\"lora_model\")\n", "tokenizer.save_pretrained(\"lora_model\")"]}], "metadata": {"kernelspec": {"display_name": "unsloth", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}