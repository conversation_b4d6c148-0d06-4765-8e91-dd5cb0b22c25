# from transformers import AutoModelForCausalLM, AutoTokenizer


# model_name = "Qwen/Qwen3-8B"
# tokenizer = AutoTokenizer.from_pretrained(model_name)
# model = AutoModelForCausalLM.from_pretrained(model_name)

# from sentence_transformers import SentenceTransformer

# try:
#     model = SentenceTransformer('BAAI/bge-m3')
#     print("\n✅ 模型 'BAAI/bge-m3' 已成功加载！文件完整无误。")
# except Exception as e:
#     print(f"\n❌ 模型加载失败，文件可能已损坏或不完整: {e}")
# from transformers import AutoModel
# model = AutoModel.from_pretrained(
#     "/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181",
#     local_files_only=True
# )
import os
import torch
from transformers import AutoModel, AutoTokenizer

# 设置国内镜像（阿里云）
os.environ["HF_ENDPOINT"] = "https://hf.aliyun.com"

# 加载模型和分词器
model = AutoModel.from_pretrained(
    "Qwen/Qwen3-Embedding-4B",
    trust_remote_code=True,
    device_map="auto"  # 自动选择GPU/CPU
)
tokenizer = AutoTokenizer.from_pretrained(
    "Qwen/Qwen3-Embedding-4B",
    trust_remote_code=True
)

# 正确生成嵌入的方法
texts = ["样例文本"]
inputs = tokenizer(texts, padding=True, return_tensors="pt").to(model.device)
with torch.no_grad():
    outputs = model(**inputs)
    embeddings = outputs.last_hidden_state.mean(dim=1)  # 取句向量

print(embeddings.shape)  # 应输出 torch.Size([1, 4096])