# from transformers import AutoModelForCausalLM, AutoTokenizer


# model_name = "Qwen/Qwen3-8B"
# tokenizer = AutoTokenizer.from_pretrained(model_name)
# model = AutoModelForCausalLM.from_pretrained(model_name)

# from sentence_transformers import SentenceTransformer

# try:
#     model = SentenceTransformer('BAAI/bge-m3')
#     print("\n✅ 模型 'BAAI/bge-m3' 已成功加载！文件完整无误。")
# except Exception as e:
#     print(f"\n❌ 模型加载失败，文件可能已损坏或不完整: {e}")
from transformers import AutoModel
model = AutoModel.from_pretrained(
    "/home/<USER>/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181",
    local_files_only=True
)