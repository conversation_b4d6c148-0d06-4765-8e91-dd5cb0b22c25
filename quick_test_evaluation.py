"""
快速测试评估框架
用少量问题测试两个系统是否能正常运行
"""

import json
import time
import sys
from pathlib import Path

def quick_test():
    """快速测试两个系统的基本功能"""
    
    print("=== 快速测试评估框架 ===\n")
    
    # 加载测试数据集
    if not Path("evaluation_test_dataset.json").exists():
        print("测试数据集不存在，请先运行 create_test_dataset.py")
        return
    
    with open("evaluation_test_dataset.json", 'r', encoding='utf-8') as f:
        test_dataset = json.load(f)
    
    # 只测试前3个问题
    test_questions = test_dataset[:3]
    print(f"使用前3个测试问题进行快速验证...")
    
    results = {
        "baseline": [],
        "improved": []
    }
    
    # 测试Baseline系统
    print("\n1. 测试Baseline系统 (AISumerCamp_multiModal_RAG)...")
    try:
        import os
        original_cwd = os.getcwd()
        baseline_dir = Path("AISumerCamp_multiModal_RAG")
        
        # 检查baseline数据
        chunk_file = baseline_dir / "all_pdf_page_chunks.json"
        if not chunk_file.exists():
            print(f"   ❌ Baseline数据文件不存在: {chunk_file}")
            print(f"   💡 请先在 {baseline_dir} 目录下运行数据生成脚本")
        else:
            print(f"   ✅ 找到Baseline数据文件")
            
            # 切换目录并测试
            os.chdir(baseline_dir)
            sys.path.insert(0, str(Path.cwd()))
            
            try:
                from rag_from_page_chunks import SimpleRAG
                
                rag = SimpleRAG("all_pdf_page_chunks.json")
                print("   🔄 初始化Baseline系统...")
                rag.setup()
                print("   ✅ Baseline系统初始化成功")
                
                # 测试问题
                for i, item in enumerate(test_questions):
                    question = item["question"]
                    print(f"   🔍 问题 {i+1}: {question[:50]}...")
                    
                    start_time = time.time()
                    result = rag.generate_answer(question, top_k=3)
                    response_time = time.time() - start_time
                    
                    answer = result.get("answer", "")[:100] + "..." if len(result.get("answer", "")) > 100 else result.get("answer", "")
                    
                    print(f"      ⏱️  响应时间: {response_time:.2f}s")
                    print(f"      📝 答案: {answer}")
                    
                    results["baseline"].append({
                        "question": question,
                        "answer": result.get("answer", ""),
                        "response_time": response_time,
                        "success": not result.get("answer", "").startswith("ERROR")
                    })
                
                print("   ✅ Baseline系统测试完成")
                    
            except Exception as e:
                print(f"   ❌ Baseline系统测试失败: {e}")
            finally:
                os.chdir(original_cwd)
                if str(baseline_dir) in sys.path:
                    sys.path.remove(str(baseline_dir))
    
    except Exception as e:
        print(f"   ❌ Baseline系统加载失败: {e}")
    
    # 测试改进系统
    print(f"\n2. 测试改进系统 (spark_multi_rag)...")
    try:
        chunk_file = Path("all_pdf_page_chunks.json")
        if not chunk_file.exists():
            print(f"   ❌ 改进系统数据文件不存在: {chunk_file}")
            print(f"   💡 请先运行 mineru_pipeline_all.py 生成数据")
        else:
            print(f"   ✅ 找到改进系统数据文件")
            
            try:
                from rag_from_page_chunks3 import AdvancedRAG
                
                rag = AdvancedRAG("all_pdf_page_chunks.json")
                print("   🔄 初始化改进系统...")
                rag.setup()
                print("   ✅ 改进系统初始化成功")
                
                # 测试问题
                for i, item in enumerate(test_questions):
                    question = item["question"]
                    print(f"   🔍 问题 {i+1}: {question[:50]}...")
                    
                    start_time = time.time()
                    result = rag.generate_answer(question, top_k_retrieve=10, top_k_rerank=3)
                    response_time = time.time() - start_time
                    
                    answer = result.get("answer", "")[:100] + "..." if len(result.get("answer", "")) > 100 else result.get("answer", "")
                    
                    print(f"      ⏱️  响应时间: {response_time:.2f}s")
                    print(f"      📝 答案: {answer}")
                    
                    results["improved"].append({
                        "question": question,
                        "answer": result.get("answer", ""),
                        "response_time": response_time,
                        "success": not result.get("answer", "").startswith("ERROR")
                    })
                
                print("   ✅ 改进系统测试完成")
                
            except Exception as e:
                print(f"   ❌ 改进系统测试失败: {e}")
    
    except Exception as e:
        print(f"   ❌ 改进系统加载失败: {e}")
    
    # 输出测试总结
    print(f"\n=== 测试总结 ===")
    
    for system_name, system_results in results.items():
        if system_results:
            success_count = sum(1 for r in system_results if r["success"])
            avg_time = sum(r["response_time"] for r in system_results) / len(system_results)
            
            print(f"\n{system_name.title()}系统:")
            print(f"  📊 成功率: {success_count}/{len(system_results)} ({success_count/len(system_results)*100:.1f}%)")
            print(f"  ⏱️  平均响应时间: {avg_time:.2f}s")
        else:
            print(f"\n{system_name.title()}系统:")
            print(f"  ❌ 测试失败，无法获得结果")
    
    # 保存测试结果
    with open("quick_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存至: quick_test_results.json")
    
    # 给出下一步建议
    print(f"\n📋 下一步操作建议:")
    if results["baseline"] and results["improved"]:
        print("✅ 两个系统都可以正常运行，可以执行完整评估:")
        print("   python run_evaluation.py")
    elif results["baseline"]:
        print("⚠️  只有Baseline系统可以运行，请检查改进系统的数据和依赖")
    elif results["improved"]:
        print("⚠️  只有改进系统可以运行，请检查Baseline系统的数据和依赖")
    else:
        print("❌ 两个系统都无法运行，请检查数据文件和依赖配置")

if __name__ == "__main__":
    quick_test()