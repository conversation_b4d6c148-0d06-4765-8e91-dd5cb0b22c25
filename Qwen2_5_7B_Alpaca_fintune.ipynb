{"cells": [{"cell_type": "markdown", "id": "04c19c23", "metadata": {}, "source": ["# 1. 安装依赖库\n", "使用pip安装Unsloth及相关依赖，确保环境准备好。\n"]}, {"cell_type": "code", "execution_count": null, "id": "6809db29", "metadata": {}, "outputs": [], "source": ["# 安装Unsloth及相关依赖库，建议在命令行或notebook中运行\n", "!pip install unsloth bitsandbytes accelerate xformers==0.0.29.post3 peft trl sentencepiece protobuf datasets huggingface_hub hf_transfer"]}, {"cell_type": "markdown", "id": "00529e58", "metadata": {}, "source": ["# 2. 加载和配置模型\n", "导入FastLanguageModel，设置最大序列长度、数据类型和是否使用4bit量化，加载Qwen2.5-7B预训练模型和分词器。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "df9abc03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/unsloth/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.7.11: Fast Qwen2 patching. Transformers: 4.54.1.\n", "   \\\\   /|    NVIDIA RTX A6000. Num GPUs = 1. Max memory: 44.988 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 8.6. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 2/2 [00:03<00:00,  1.50s/it]\n"]}], "source": ["# 导入FastLanguageModel和torch\n", "from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048  # 最大序列长度，可根据显存调整\n", "dtype = None  # 自动检测数据类型，推荐float16或bfloat16\n", "load_in_4bit = True  # 是否使用4bit量化，节省显存\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Qwen2.5-7B\",  # 选择Qwen2.5-7B模型\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # 如需访问受限模型请填写token\n", " )"]}, {"cell_type": "markdown", "id": "9b7552ad", "metadata": {}, "source": ["# 3. 添加LoRA适配器\n", "为模型添加LoRA适配器，只微调部分参数以节省显存和加速训练。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7e1a98f7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.7.11 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}], "source": ["# 为模型添加LoRA适配器，只微调部分参数，节省显存\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16,  # <PERSON><PERSON>秩，越大可微调参数越多\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0,  # 推荐为0，优化显存\n", "    bias = \"none\",\n", "    use_gradient_checkpointing = \"unsloth\",  # 支持长上下文\n", "    random_state = 3407,\n", "    use_rslora = False,\n", "    loftq_config = None,\n", " )"]}, {"cell_type": "markdown", "id": "3d1be69c", "metadata": {}, "source": ["# 4. 准备Alpaca数据集\n", "加载Alpaca中文数据集，也可替换为自定义数据集。\n"]}, {"cell_type": "code", "execution_count": null, "id": "b624e586", "metadata": {}, "outputs": [], "source": ["# 加载Alpaca数据集，可替换为自定义数据集\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"yahma/alpaca-cleaned\", split=\"train\")  # 默认加载英文，可替换为中文或自定义数据"]}, {"cell_type": "code", "execution_count": 11, "id": "b7100a97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset({\n", "    features: ['instruction', 'input', 'output', 'text'],\n", "    num_rows: 51760\n", "})\n", "{'instruction': 'Give three tips for staying healthy.', 'input': '', 'output': '1. Eat a balanced and nutritious diet: Make sure your meals are inclusive of a variety of fruits and vegetables, lean protein, whole grains, and healthy fats. This helps to provide your body with the essential nutrients to function at its best and can help prevent chronic diseases.\\n\\n2. Engage in regular physical activity: Exercise is crucial for maintaining strong bones, muscles, and cardiovascular health. Aim for at least 150 minutes of moderate aerobic exercise or 75 minutes of vigorous exercise each week.\\n\\n3. Get enough sleep: Getting enough quality sleep is crucial for physical and mental well-being. It helps to regulate mood, improve cognitive function, and supports healthy growth and immune function. Aim for 7-9 hours of sleep each night.', 'text': '以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\\n\\n### 指令:\\nGive three tips for staying healthy.\\n\\n### 输入:\\n\\n\\n### 回复:\\n1. Eat a balanced and nutritious diet: Make sure your meals are inclusive of a variety of fruits and vegetables, lean protein, whole grains, and healthy fats. This helps to provide your body with the essential nutrients to function at its best and can help prevent chronic diseases.\\n\\n2. Engage in regular physical activity: Exercise is crucial for maintaining strong bones, muscles, and cardiovascular health. Aim for at least 150 minutes of moderate aerobic exercise or 75 minutes of vigorous exercise each week.\\n\\n3. Get enough sleep: Getting enough quality sleep is crucial for physical and mental well-being. It helps to regulate mood, improve cognitive function, and supports healthy growth and immune function. Aim for 7-9 hours of sleep each night.<|endoftext|>'}\n"]}], "source": ["# 打印数据集看看\n", "print(dataset)\n", "print(dataset[0])  # 打印第一条数据"]}, {"cell_type": "markdown", "id": "7d7cdec9", "metadata": {}, "source": ["# 5. 格式化数据并添加EOS标记\n", "定义格式化函数，将指令、输入和输出拼接为训练文本，并在末尾添加EOS标记。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "de5cc8d5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 51760/51760 [00:00<00:00, 89056.92 examples/s]\n"]}], "source": ["# 定义格式化函数，将指令、输入、输出拼接为训练文本，并添加EOS标记\n", "alpaca_prompt = \"\"\"以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\\n\\n### 指令:\\n{}\\n\\n### 输入:\\n{}\\n\\n### 回复:\\n{}\"\"\"\n", "EOS_TOKEN = tokenizer.eos_token  # 获取模型的EOS标记\n", "def formatting_prompts_func(examples):\n", "    instructions = examples[\"instruction\"]\n", "    inputs = examples[\"input\"]\n", "    outputs = examples[\"output\"]\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        # 拼接指令、输入和输出，并添加EOS标记\n", "        text = alpaca_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return {\"text\": texts}\n", "dataset = dataset.map(formatting_prompts_func, batched=True)"]}, {"cell_type": "markdown", "id": "bfd8f035", "metadata": {}, "source": ["# 6. 训练模型\n", "使用TRL的SFTTrainer进行微调，设置训练参数如批次大小、学习率、训练步数等。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "53e98fea", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: Tokenizing [\"text\"] (num_proc=2): 100%|██████████| 51760/51760 [00:15<00:00, 3264.68 examples/s]\n"]}], "source": ["# 使用SFTTrainer进行微调，设置训练参数\n", "from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    packing = False,  # 短序列可加速训练\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        max_steps = 60,  # 训练步数，可根据需求调整\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"model_train_outputs\",\n", "        report_to = \"none\",  # 可接入WandB等工具\n", "    ),\n", ")"]}, {"cell_type": "markdown", "id": "702363df", "metadata": {}, "source": ["# 7. 显示显存信息\n", "通过torch.cuda获取和打印显卡显存使用情况，便于监控资源消耗。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a4df204b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU型号: NVIDIA RTX A6000，最大显存: 44.988 GB\n", "已预留显存: 7.246 GB\n"]}], "source": ["# 显示当前GPU显存信息，便于监控资源消耗\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU型号: {gpu_stats.name}，最大显存: {max_memory} GB\")\n", "print(f\"已预留显存: {start_gpu_memory} GB\")"]}, {"cell_type": "code", "execution_count": 9, "id": "6c8a8357", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 51,760 | Num Epochs = 1 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 40,370,176 of 7,655,986,688 (0.53% trained)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 02:51, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.370200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.775800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.450700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.696800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.580400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.378600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.965500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.191100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.072400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.986400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.791500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.884500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.758400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.992000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.774800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.730000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.913900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.266000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.927200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.756200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.833500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.926800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.905400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.931900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.995100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.967200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.984600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.836000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.861700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.718300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.819800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.792000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.942400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.797400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.910700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.785200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.764900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.668200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>1.003800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>1.053100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.843900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.907300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.852000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.850500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.921400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.888500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.734200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.181300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.822300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.019000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.980300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.857500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.950300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>1.064000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.729500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.977200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.816300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.708700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.755700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.826000</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["训练耗时: 180.6005 秒\n", "训练耗时: 3.01 分钟\n", "训练期间峰值显存: 8.635 GB，占最大显存 19.194%\n", "LoRA训练显存: 1.389 GB，占最大显存 3.087%\n"]}], "source": ["# 开始训练模型，并显示训练结果和显存变化\n", "trainer_stats = trainer.train()\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"训练耗时: {trainer_stats.metrics['train_runtime']} 秒\")\n", "print(f\"训练耗时: {round(trainer_stats.metrics['train_runtime']/60, 2)} 分钟\")\n", "print(f\"训练期间峰值显存: {used_memory} GB，占最大显存 {used_percentage}%\")\n", "print(f\"LoRA训练显存: {used_memory_for_lora} GB，占最大显存 {lora_percentage}%\")"]}, {"cell_type": "markdown", "id": "7494c7e6", "metadata": {}, "source": ["# 8. 模型推理（生成文本）\n", "启用推理模式，输入指令和上下文，生成模型输出，并展示如何使用TextStreamer流式输出结果。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "5ecec938", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\\n\\n### 指令:\\n请续写斐波那契数列。\\n\\n### 输入:\\n1, 1, 2, 3, 5, 8\\n\\n### 回复:\\n13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6']\n", "以下是一条描述任务的指令，配有进一步的输入信息。请根据要求完成回复。\n", "\n", "### 指令:\n", "请续写斐波那契数列。\n", "\n", "### 输入:\n", "1, 1, 2, 3, 5, 8\n", "\n", "### 回复:\n", "13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765, 10946, 17711, 28657, 46368, 75077, 121393, 193491, 194676, \n"]}], "source": ["# 启用推理模式，输入指令和上下文，生成模型输出\n", "FastLanguageModel.for_inference(model)  # 开启高效推理模式\n", "inputs = tokenizer([\n", "    alpaca_prompt.format(\n", "        \"请续写斐波那契数列。\",  # 指令\n", "        \"1, 1, 2, 3, 5, 8\",  # 输入\n", "        \"\",  # 输出留空，模型自动生成\n", "    )\n", "], return_tensors=\"pt\").to(\"cuda\")\n", "outputs = model.generate(**inputs, max_new_tokens=64, use_cache=True)\n", "print(tokenizer.batch_decode(outputs))  # 输出生成结果\n", "\n", "# 使用TextStreamer流式输出结果\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer=text_streamer, max_new_tokens=128)"]}, {"cell_type": "markdown", "id": "b4211a10", "metadata": {}, "source": ["# 9. 保存和加载微调后的模型\n", "保存LoRA适配器和分词器到本地或上传到Hugging Face Hub，并演示如何重新加载用于推理。\n"]}, {"cell_type": "code", "execution_count": null, "id": "bcc4ee39", "metadata": {}, "outputs": [], "source": ["# 保存LoRA适配器和分词器到本地\n", "model.save_pretrained(\"lora_model\")\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# 上传到Hugging Face Hub（需填写token）\n", "# model.push_to_hub(\"你的用户名/lora_model\", token=\"你的token\")\n", "# tokenizer.push_to_hub(\"你的用户名/lora_model\", token=\"你的token\")\n", "\n", "# 重新加载微调后的模型用于推理\n", "if False:\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\",\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model)"]}, {"cell_type": "markdown", "id": "f2497ef6", "metadata": {}, "source": ["# 10. 保存为float16或GGUF格式\n", "展示如何将模型保存为float16或GGUF格式，支持VLLM和llama.cpp等推理框架。\n"]}, {"cell_type": "code", "execution_count": null, "id": "8185a726", "metadata": {}, "outputs": [], "source": ["# 保存为float16格式，适用于VLLM等框架\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method=\"merged_16bit\")\n", "if False: model.push_to_hub_merged(\"你的用户名/model\", tokenizer, save_method=\"merged_16bit\", token=\"你的token\")\n", "\n", "# 保存为GGUF格式，适用于llama.cpp等框架\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method=\"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"你的用户名/model\", tokenizer, quantization_method=\"q4_k_m\", token=\"你的token\")"]}], "metadata": {"kernelspec": {"display_name": "unsloth", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}