#!/usr/bin/env python3
"""
测试Vision API的完整输出
"""

import asyncio
import os
from image_utils.async_image_analysis import AsyncImageAnalysis
from dotenv import load_dotenv
load_dotenv()

async def test_vision_full_output():
    # 测试一个具体的图片
    image_path = "/home/<USER>/lhp/projects/0806RAG/spark_multi_rag/data_base_json_content/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/千味央厨-百味千寻餐饮供应链龙头正崛起-21091628页/auto/images/16c63cfaf9dcc4f955c067abe27060a2245bfc9938f765f9999369f164c7bc01.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return
    
    vision_provider = "guiji"
    vision_model = os.getenv("LOCAL_VISION_MODEL", "Pro/Qwen/Qwen2.5-VL-7B-Instruct")
    vision_api_key = os.getenv("LOCAL_API_KEY")
    vision_base_url = os.getenv("LOCAL_BASE_URL")
    
    print("🧪 测试Vision API完整输出")
    print(f"📸 测试图片: {image_path}")
    print(f"🤖 模型: {vision_model}")
    print()
    
    try:
        async with AsyncImageAnalysis(
            provider=vision_provider,
            api_key=vision_api_key,
            base_url=vision_base_url,
            vision_model=vision_model
        ) as analyzer:
            result = await analyzer.analyze_image(local_image_path=image_path)
            
            print("📊 完整API响应:")
            print(f"类型: {type(result)}")
            print(f"键: {result.keys() if isinstance(result, dict) else 'N/A'}")
            print()
            
            description = result.get('description', '')
            title = result.get('title', '')
            
            print("📝 完整描述 (description):")
            print(f"长度: {len(description)} 字符")
            print(f"内容: {description}")
            print()
            
            print("📋 标题 (title):")
            print(f"长度: {len(title)} 字符")
            print(f"内容: {title}")
            print()
            
            enhanced_caption = description if description else title
            print("🎯 最终使用的增强描述:")
            print(f"长度: {len(enhanced_caption)} 字符")
            print(f"内容: {enhanced_caption}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def main():
    print("=" * 60)
    print("🔬 Vision API完整输出测试")
    print("=" * 60)
    
    asyncio.run(test_vision_full_output())

if __name__ == '__main__':
    main()