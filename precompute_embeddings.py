#!/usr/bin/env python3
"""
预计算嵌入向量脚本
一次性生成并保存嵌入向量，避免每次评估时重新计算
"""

import json
import numpy as np
import pickle
import os
from tqdm import tqdm
from sentence_transformers import SentenceTransformer

def precompute_embeddings():
    """预计算并保存嵌入向量"""
    print("开始预计算嵌入向量...")
    
    # 加载文档块
    chunks_path = "all_pdf_page_chunks.json"
    with open(chunks_path, 'r', encoding='utf-8') as f:
        chunks = json.load(f)
    
    print(f"加载了 {len(chunks)} 个文档块")
    
    # 初始化嵌入模型
    model_name = 'BAAI/bge-m3'
    print(f"初始化嵌入模型: {model_name}")
    model = SentenceTransformer(model_name)
    
    # 提取文本内容
    texts = [chunk['content'] for chunk in chunks]
    
    # 生成嵌入向量
    print("生成嵌入向量...")
    embeddings = model.encode(
        texts, 
        batch_size=8,  # 小批次大小避免内存问题
        show_progress_bar=True,
        normalize_embeddings=True
    )
    
    # 保存嵌入向量
    embeddings_path = "precomputed_embeddings.pkl"
    with open(embeddings_path, 'wb') as f:
        pickle.dump({
            'embeddings': embeddings,
            'chunks': chunks,
            'model_name': model_name
        }, f)
    
    print(f"✅ 嵌入向量已保存到: {embeddings_path}")
    print(f"向量维度: {embeddings.shape}")
    print(f"文件大小: {os.path.getsize(embeddings_path) / 1024 / 1024:.2f} MB")

if __name__ == "__main__":
    precompute_embeddings()