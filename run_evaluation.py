"""
RAG系统性能对比评估主脚本
对比两套完整方案：
1. AISumerCamp_multiModal_RAG (Baseline方案)
2. spark_multi_rag (改进方案，支持Vision增强)
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List
import pandas as pd
from tqdm import tqdm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SystemEvaluator:
    """系统评估器"""
    
    def __init__(self, test_dataset_path: str = "evaluation_test_dataset.json", max_test_count: int = None, skip_baseline: bool = False):
        """初始化评估器"""
        self.test_dataset_path = test_dataset_path
        self.results = {}
        self.skip_baseline = skip_baseline
        
        # 加载测试数据集
        with open(test_dataset_path, 'r', encoding='utf-8') as f:
            self.test_dataset = json.load(f)
        
        # 限制测试数量
        original_count = len(self.test_dataset)
        if max_test_count is not None and max_test_count < original_count:
            self.test_dataset = self.test_dataset[:max_test_count]
            print(f"限制测试数量为 {max_test_count}，从原始 {original_count} 个问题中选择前 {max_test_count} 个")
        
        print(f"加载测试数据集，共 {len(self.test_dataset)} 个问题")
        if skip_baseline:
            print("⚠️  将跳过Baseline系统评估，使用现有结果")
        
    def load_existing_baseline_results(self) -> List[Dict]:
        """加载现有的Baseline评估结果"""
        baseline_file = "baseline_detailed_results.json"
        try:
            with open(baseline_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            print(f"✅ 成功加载现有Baseline结果：{len(results)} 个问题")
            return results
        except FileNotFoundError:
            print(f"❌ 找不到现有Baseline结果文件：{baseline_file}")
            print("将执行完整的Baseline评估...")
            return self.run_baseline_system_full()
        except Exception as e:
            print(f"❌ 加载Baseline结果失败：{e}")
            print("将执行完整的Baseline评估...")
            return self.run_baseline_system_full()

    def run_baseline_system(self) -> List[Dict]:
        """运行Baseline系统 (根据设置决定是否跳过)"""
        if self.skip_baseline:
            return self.load_existing_baseline_results()
        else:
            return self.run_baseline_system_full()
    
    def run_baseline_system_full(self) -> List[Dict]:
        """完整运行Baseline系统 (AISumerCamp_multiModal_RAG)"""
        print("\n=== 运行Baseline系统 (AISumerCamp_multiModal_RAG) ===")
        
        results = []
        baseline_dir = Path("AISumerCamp_multiModal_RAG")
        
        # 检查baseline数据是否存在
        chunk_file = baseline_dir / "all_pdf_page_chunks.json"
        if not chunk_file.exists():
            print(f"Baseline数据文件不存在: {chunk_file}")
            print("尝试生成baseline数据...")
            
            # 切换到baseline目录并运行数据生成
            original_cwd = os.getcwd()
            try:
                os.chdir(baseline_dir)
                
                # 先检查是否有PDF处理的结果，如果没有就用fitz方法快速生成
                if not Path("all_pdf_page_chunks.json").exists():
                    print("使用fitz_pipeline_all.py生成baseline数据...")
                    result = subprocess.run([
                        sys.executable, "fitz_pipeline_all.py"
                    ], capture_output=True, text=True, timeout=600)
                    
                    if result.returncode != 0:
                        print(f"Baseline数据生成失败: {result.stderr}")
                        return []
                
            finally:
                os.chdir(original_cwd)
        
        # 运行baseline RAG系统
        try:
            original_cwd = os.getcwd()
            os.chdir(baseline_dir)
            
            # 导入baseline的RAG系统
            baseline_path = str(Path.cwd())
            sys.path.insert(0, baseline_path)
            from rag_from_page_chunks import SimpleRAG
            
            # 初始化系统
            rag = SimpleRAG("all_pdf_page_chunks.json")
            rag.setup()
            
            # 对每个测试问题进行评估
            for item in tqdm(self.test_dataset, desc="Baseline评估"):
                question = item["question"]
                
                start_time = time.time()
                try:
                    result = rag.generate_answer(question, top_k=5)
                    response_time = time.time() - start_time
                    
                    # 获取timing信息
                    timing = result.get("timing", {})
                    eval_result = {
                        "question": question,
                        "answer": result.get("answer", ""),
                        "filename": result.get("filename", ""),
                        "page": result.get("page", ""),
                        "response_time": response_time,
                        "system": "Baseline",
                        "vision_enhanced": False,
                        "retrieval_chunks_count": len(result.get("retrieval_chunks", [])),
                        # 添加检索相关时间指标
                        "retrieval_time": timing.get("retrieval_time", 0),
                        "fusion_time": timing.get("fusion_time", 0),
                        "rerank_time": timing.get("rerank_time", 0),
                        "generation_time": timing.get("generation_time", 0)
                    }
                except Exception as e:
                    response_time = time.time() - start_time
                    eval_result = {
                        "question": question,
                        "answer": f"ERROR: {str(e)}",
                        "filename": "",
                        "page": "",
                        "response_time": response_time,
                        "system": "Baseline",
                        "vision_enhanced": False,
                        "retrieval_chunks_count": 0,
                        # 错误情况下的时间指标
                        "retrieval_time": 0,
                        "fusion_time": 0,
                        "rerank_time": 0,
                        "generation_time": 0
                    }
                
                results.append(eval_result)
            
        finally:
            os.chdir(original_cwd)
            if baseline_path in sys.path:
                sys.path.remove(baseline_path)
        
        print(f"Baseline系统评估完成，共处理 {len(results)} 个问题")
        return results
    
    def run_improved_system(self) -> List[Dict]:
        """运行改进系统 (spark_multi_rag)"""
        print("\n=== 运行改进系统 (spark_multi_rag) ===")
        
        results = []
        
        # 检查改进系统数据是否存在
        chunk_file = Path("all_pdf_page_chunks.json")
        if not chunk_file.exists():
            print("改进系统数据文件不存在，尝试生成...")
            
            # 运行mineru pipeline生成数据
            try:
                result = subprocess.run([
                    sys.executable, "mineru_pipeline_all.py"
                ], capture_output=True, text=True, timeout=1200)  # 20分钟超时
                
                if result.returncode != 0:
                    print(f"改进系统数据生成失败: {result.stderr}")
                    return []
                    
            except subprocess.TimeoutExpired:
                print("数据生成超时，使用现有数据")
        
        # 运行改进的RAG系统
        try:
            from rag_from_page_chunks4 import AdvancedRAG
            
            # 初始化系统
            rag = AdvancedRAG("all_pdf_page_chunks.json")
            rag.setup()
            
            # 对每个测试问题进行评估
            for item in tqdm(self.test_dataset, desc="改进系统评估"):
                question = item["question"]
                
                start_time = time.time()
                try:
                    result = rag.generate_answer(question)  # 使用优化参数
                    response_time = time.time() - start_time
                    
                    # 获取timing信息
                    timing = result.get("timing", {})
                    eval_result = {
                        "question": question,
                        "answer": result.get("answer", ""),
                        "filename": result.get("filename", ""),
                        "page": result.get("page", ""),
                        "response_time": response_time,
                        "system": "Optimized_v4",  # 标记为优化版本v4
                        "vision_enhanced": True,  # v4支持vision
                        "retrieval_chunks_count": len(result.get("retrieval_chunks", [])),
                        "optimization_applied": "RRF_k60_retrieve20_rerank5",  # 更新优化配置
                        # 添加检索相关时间指标
                        "retrieval_time": timing.get("retrieval_time", 0),
                        "fusion_time": timing.get("fusion_time", 0),
                        "rerank_time": timing.get("rerank_time", 0),
                        "generation_time": timing.get("generation_time", 0)
                    }
                except Exception as e:
                    response_time = time.time() - start_time
                    eval_result = {
                        "question": question,
                        "answer": f"ERROR: {str(e)}",
                        "filename": "",
                        "page": "",
                        "response_time": response_time,
                        "system": "Optimized_v4",
                        "vision_enhanced": False,
                        "retrieval_chunks_count": 0,
                        "optimization_applied": "RRF_k60_retrieve20_rerank5",
                        # 错误情况下的时间指标
                        "retrieval_time": 0,
                        "fusion_time": 0,
                        "rerank_time": 0,
                        "generation_time": 0
                    }
                
                results.append(eval_result)
                
        except Exception as e:
            print(f"改进系统运行失败: {e}")
            return []
        
        print(f"改进系统评估完成，共处理 {len(results)} 个问题")
        return results
    
    def calculate_metrics(self, results: List[Dict]) -> Dict[str, float]:
        """计算系统指标"""
        if not results:
            return {}
        
        total_questions = len(results)
        successful_answers = len([r for r in results if not r["answer"].startswith("ERROR")])
        
        metrics = {
            "成功率": successful_answers / total_questions,
            "平均响应时间": np.mean([r["response_time"] for r in results]),
            "响应时间中位数": np.median([r["response_time"] for r in results]),
            "平均检索块数": np.mean([r["retrieval_chunks_count"] for r in results]),
        }
        
        # 添加检索相关指标
        if any("retrieval_time" in r for r in results):
            retrieval_times = [r.get("retrieval_time", 0) for r in results]
            fusion_times = [r.get("fusion_time", 0) for r in results]
            rerank_times = [r.get("rerank_time", 0) for r in results]
            generation_times = [r.get("generation_time", 0) for r in results]
            
            metrics.update({
                "平均检索延迟": np.mean(retrieval_times),
                "检索延迟中位数": np.median(retrieval_times),
                "平均融合时间": np.mean(fusion_times),
                "平均重排序时间": np.mean(rerank_times),
                "平均生成时间": np.mean(generation_times),
                "检索时间占比": np.mean([rt / max(rt + ft + rrt + gt, 0.001) for rt, ft, rrt, gt in zip(retrieval_times, fusion_times, rerank_times, generation_times)])
            })
            
        # 简化的检索准确率评估（启发式）
        retrieval_precision_scores = []
        for result in results:
            if not result["answer"].startswith("ERROR") and result["filename"] and result["page"]:
                # 如果答案不为空且有文件来源，认为检索效果较好
                precision_score = 0.8
                # 如果答案较长且包含具体信息，加分
                if len(result["answer"]) > 50 and any(keyword in result["answer"] for keyword in ['数据', '百分比', '万元', '增长', '下降']):
                    precision_score = 0.9
            elif not result["answer"].startswith("ERROR") and result["answer"].strip():
                # 有答案但没有文件来源
                precision_score = 0.4
            else:
                # 没有答案或错误
                precision_score = 0.0
            retrieval_precision_scores.append(precision_score)
        
        metrics["检索准确率估值"] = np.mean(retrieval_precision_scores)
        
        # 计算答案质量分数（简化版本）
        quality_scores = []
        for result in results:
            answer = result["answer"]
            if answer.startswith("ERROR"):
                score = 0.0
            elif not answer.strip():
                score = 0.1
            elif len(answer) < 10:
                score = 0.3
            elif len(answer) > 500:
                score = 0.7  # 可能过于冗长
            else:
                score = 0.8
                # 如果有文件名和页码信息，额外加分
                if result["filename"] and result["page"]:
                    score += 0.2
        
            quality_scores.append(min(score, 1.0))
        
        metrics["平均答案质量"] = np.mean(quality_scores)
        
        return metrics
    
    def compare_vision_enhancement(self, baseline_results: List[Dict], improved_results: List[Dict]) -> Dict[str, Any]:
        """比较Vision增强效果"""
        print("\n=== 分析Vision增强效果 ===")
        
        # 找出需要视觉分析的问题
        vision_questions = [
            i for i, item in enumerate(self.test_dataset) 
            if item.get("requires_vision", False)
        ]
        
        if not vision_questions:
            return {"vision_enhancement_score": 0.0, "analysis": "没有需要视觉分析的问题"}
        
        print(f"找到 {len(vision_questions)} 个需要视觉分析的问题")
        
        baseline_vision_scores = []
        improved_vision_scores = []
        
        for idx in vision_questions:
            if idx < len(baseline_results) and idx < len(improved_results):
                # 简单的答案质量评估
                baseline_score = self._score_answer_quality(baseline_results[idx]["answer"])
                improved_score = self._score_answer_quality(improved_results[idx]["answer"])
                
                baseline_vision_scores.append(baseline_score)
                improved_vision_scores.append(improved_score)
        
        if not baseline_vision_scores:
            return {"vision_enhancement_score": 0.0, "analysis": "无法进行Vision效果对比"}
        
        baseline_avg = np.mean(baseline_vision_scores)
        improved_avg = np.mean(improved_vision_scores)
        
        enhancement_score = max(0, improved_avg - baseline_avg)
        
        return {
            "vision_enhancement_score": enhancement_score,
            "baseline_vision_avg": baseline_avg,
            "improved_vision_avg": improved_avg,
            "vision_questions_count": len(vision_questions),
            "improvement_ratio": (improved_avg / baseline_avg - 1) * 100 if baseline_avg > 0 else 0
        }
    
    def _score_answer_quality(self, answer: str) -> float:
        """简单的答案质量评分"""
        if not answer or answer.startswith("ERROR"):
            return 0.0
        
        score = 0.5  # 基础分
        
        # 长度合理性
        if 20 <= len(answer) <= 300:
            score += 0.2
        elif 10 <= len(answer) < 20:
            score += 0.1
        
        # 包含具体信息
        info_keywords = ["数据", "图表", "显示", "表格", "图", "统计", "数字", "百分比", "增长", "下降"]
        if any(keyword in answer for keyword in info_keywords):
            score += 0.3
        
        return min(score, 1.0)
    
    def create_comparison_report(self, baseline_results: List[Dict], improved_results: List[Dict]) -> Dict[str, Any]:
        """生成对比报告"""
        print("\n=== 生成对比报告 ===")
        
        baseline_metrics = self.calculate_metrics(baseline_results)
        improved_metrics = self.calculate_metrics(improved_results)
        vision_analysis = self.compare_vision_enhancement(baseline_results, improved_results)
        
        # 按问题类别分析
        category_analysis = {}
        for category in set(item["category"] for item in self.test_dataset):
            category_indices = [i for i, item in enumerate(self.test_dataset) if item["category"] == category]
            
            if category_indices:
                baseline_cat = [baseline_results[i] for i in category_indices if i < len(baseline_results)]
                improved_cat = [improved_results[i] for i in category_indices if i < len(improved_results)]
                
                category_analysis[category] = {
                    "问题数量": len(category_indices),
                    "Baseline指标": self.calculate_metrics(baseline_cat) if baseline_cat else {},
                    "改进系统指标": self.calculate_metrics(improved_cat) if improved_cat else {}
                }
        
        report = {
            "评估概要": {
                "测试问题总数": len(self.test_dataset),
                "评估时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "Baseline系统": "AISumerCamp_multiModal_RAG",
                "改进系统": "spark_multi_rag"
            },
            "整体性能对比": {
                "Baseline": baseline_metrics,
                "Improved": improved_metrics
            },
            "Vision增强效果分析": vision_analysis,
            "分类别性能分析": category_analysis,
            "性能改进总结": self._calculate_improvements(baseline_metrics, improved_metrics)
        }
        
        return report
    
    def _calculate_improvements(self, baseline_metrics: Dict, improved_metrics: Dict) -> Dict[str, str]:
        """计算性能改进"""
        improvements = {}
        
        for metric_name in baseline_metrics:
            if metric_name in improved_metrics:
                baseline_val = baseline_metrics[metric_name]
                improved_val = improved_metrics[metric_name]
                
                if baseline_val > 0:
                    if metric_name == "平均响应时间" or metric_name == "响应时间中位数":
                        # 响应时间越低越好
                        improvement = (baseline_val - improved_val) / baseline_val * 100
                        if improvement > 0:
                            improvements[metric_name] = f"提升 {improvement:.1f}% (更快)"
                        else:
                            improvements[metric_name] = f"下降 {abs(improvement):.1f}% (更慢)"
                    else:
                        # 其他指标越高越好
                        improvement = (improved_val - baseline_val) / baseline_val * 100
                        if improvement > 0:
                            improvements[metric_name] = f"提升 {improvement:.1f}%"
                        else:
                            improvements[metric_name] = f"下降 {abs(improvement):.1f}%"
        
        return improvements
    
    def _analyze_retrieval_performance(self, baseline_metrics: Dict, improved_metrics: Dict) -> Dict[str, str]:
        """分析检索性能对比"""
        analysis = {}
        
        # 检索延迟对比
        if "平均检索延迟" in baseline_metrics and "平均检索延迟" in improved_metrics:
            baseline_retrieval = baseline_metrics["平均检索延迟"]
            improved_retrieval = improved_metrics["平均检索延迟"]
            if baseline_retrieval > 0:
                improvement = (baseline_retrieval - improved_retrieval) / baseline_retrieval * 100
                if improvement > 0:
                    analysis["检索延迟"] = f"提升 {improvement:.1f}% (更快)"
                else:
                    analysis["检索延迟"] = f"下降 {abs(improvement):.1f}% (更慢)"
        
        # 检索准确率对比
        if "检索准确率估值" in baseline_metrics and "检索准确率估值" in improved_metrics:
            baseline_precision = baseline_metrics["检索准确率估值"]
            improved_precision = improved_metrics["检索准确率估值"]
            if baseline_precision > 0:
                improvement = (improved_precision - baseline_precision) / baseline_precision * 100
                if improvement > 0:
                    analysis["检索准确率"] = f"提升 {improvement:.1f}%"
                else:
                    analysis["检索准确率"] = f"下降 {abs(improvement):.1f}%"
        
        # 检索时间占比对比
        if "检索时间占比" in baseline_metrics and "检索时间占比" in improved_metrics:
            baseline_ratio = baseline_metrics["检索时间占比"]
            improved_ratio = improved_metrics["检索时间占比"]
            analysis["检索时间占比"] = f"Baseline: {baseline_ratio:.1%}, Improved: {improved_ratio:.1%}"
            
        # 混合检索优势分析
        if "平均融合时间" in improved_metrics and "平均重排序时间" in improved_metrics:
            fusion_time = improved_metrics["平均融合时间"]
            rerank_time = improved_metrics["平均重排序时间"]
            analysis["混合检索额外开销"] = f"融合: {fusion_time:.3f}s, 重排序: {rerank_time:.3f}s"
            
        return analysis
    
    def create_visualizations(self, report: Dict[str, Any]):
        """创建可视化图表"""
        print("\n=== 创建可视化图表 ===")
        
        baseline_metrics = report["整体性能对比"]["Baseline"]
        improved_metrics = report["整体性能对比"]["Improved"]
        
        # 创建对比图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('RAG系统性能对比评估结果', fontsize=16)
        
        # 1. 成功率对比
        systems = ['Baseline', 'Improved']
        success_rates = [baseline_metrics.get("成功率", 0), improved_metrics.get("成功率", 0)]
        
        axes[0, 0].bar(systems, success_rates, color=['skyblue', 'lightcoral'])
        axes[0, 0].set_title('成功率对比')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].set_ylim(0, 1)
        
        for i, v in enumerate(success_rates):
            axes[0, 0].text(i, v + 0.01, f'{v:.2f}', ha='center', va='bottom')
        
        # 2. 响应时间对比
        response_times = [baseline_metrics.get("平均响应时间", 0), improved_metrics.get("平均响应时间", 0)]
        
        axes[0, 1].bar(systems, response_times, color=['skyblue', 'lightcoral'])
        axes[0, 1].set_title('平均响应时间对比')
        axes[0, 1].set_ylabel('时间(秒)')
        
        for i, v in enumerate(response_times):
            axes[0, 1].text(i, v + max(response_times)*0.01, f'{v:.2f}s', ha='center', va='bottom')
        
        # 3. 答案质量对比
        quality_scores = [baseline_metrics.get("平均答案质量", 0), improved_metrics.get("平均答案质量", 0)]
        
        axes[1, 0].bar(systems, quality_scores, color=['skyblue', 'lightcoral'])
        axes[1, 0].set_title('平均答案质量对比')
        axes[1, 0].set_ylabel('质量分数')
        axes[1, 0].set_ylim(0, 1)
        
        for i, v in enumerate(quality_scores):
            axes[1, 0].text(i, v + 0.01, f'{v:.2f}', ha='center', va='bottom')
        
        # 4. Vision增强效果
        vision_data = report["Vision增强效果分析"]
        if "baseline_vision_avg" in vision_data and "improved_vision_avg" in vision_data:
            vision_scores = [vision_data["baseline_vision_avg"], vision_data["improved_vision_avg"]]
            
            axes[1, 1].bar(systems, vision_scores, color=['skyblue', 'lightcoral'])
            axes[1, 1].set_title(f'Vision增强效果对比\n({vision_data["vision_questions_count"]}个视觉问题)')
            axes[1, 1].set_ylabel('平均质量分数')
            axes[1, 1].set_ylim(0, 1)
            
            for i, v in enumerate(vision_scores):
                axes[1, 1].text(i, v + 0.01, f'{v:.2f}', ha='center', va='bottom')
        else:
            axes[1, 1].text(0.5, 0.5, 'Vision增强数据不足', ha='center', va='center')
            axes[1, 1].set_title('Vision增强效果对比')
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = "evaluation_results_comparison2.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"对比图表已保存至: {chart_path}")
        
        plt.show()
    
    def run_full_evaluation(self):
        """运行完整评估"""
        print("开始RAG系统对比评估...")
        
        # 运行两个系统
        baseline_results = self.run_baseline_system()
        improved_results = self.run_improved_system()
        
        if not baseline_results and not improved_results:
            print("评估失败：两个系统都无法运行")
            return
        
        # 生成对比报告
        report = self.create_comparison_report(baseline_results, improved_results)
        
        # 保存详细结果
        with open("baseline_detailed_results.json", 'w', encoding='utf-8') as f:
            json.dump(baseline_results, f, ensure_ascii=False, indent=2)
        
        with open("improved_detailed_results2.json", 'w', encoding='utf-8') as f:
            json.dump(improved_results, f, ensure_ascii=False, indent=2)
        
        with open("evaluation_comparison_report2.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印关键结果
        self.print_summary(report)
        
        # 创建可视化
        self.create_visualizations(report)
        
        print(f"\n评估完成！详细结果已保存至:")
        print(f"- baseline_detailed_results.json")
        print(f"- improved_detailed_results2.json")
        print(f"- evaluation_comparison_report2.json")
        print(f"- evaluation_results_comparison2.png")
    
    def print_summary(self, report: Dict[str, Any]):
        """打印评估总结"""
        print("\n" + "="*60)
        print("RAG系统性能对比评估总结")
        print("="*60)
        
        baseline_metrics = report["整体性能对比"]["Baseline"]
        improved_metrics = report["整体性能对比"]["Improved"]
        
        print(f"\n📊 整体性能对比:")
        print(f"{'指标':<15} {'Baseline':<12} {'Improved':<12} {'改进情况'}")
        print("-" * 60)
        
        for metric_name in baseline_metrics:
            if metric_name in improved_metrics:
                baseline_val = baseline_metrics[metric_name]
                improved_val = improved_metrics[metric_name]
                improvement = report["性能改进总结"].get(metric_name, "N/A")
                
                print(f"{metric_name:<15} {baseline_val:<12.3f} {improved_val:<12.3f} {improvement}")
        
        vision_analysis = report["Vision增强效果分析"]
        if "improvement_ratio" in vision_analysis:
            print(f"\n🔍 Vision增强效果:")
            print(f"  - 视觉问题数量: {vision_analysis['vision_questions_count']}")
            print(f"  - 改进幅度: {vision_analysis['improvement_ratio']:.1f}%")
            print(f"  - 增强分数: {vision_analysis['vision_enhancement_score']:.3f}")
        
        # 添加检索性能对比
        print(f"\n🔍 检索性能对比:")
        retrieval_comparison = self._analyze_retrieval_performance(baseline_metrics, improved_metrics)
        for key, value in retrieval_comparison.items():
            print(f"  - {key}: {value}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG系统性能对比评估")
    parser.add_argument("--skip-baseline", action="store_true", 
                       help="跳过Baseline系统评估，使用现有结果（节省时间）")
    parser.add_argument("--test-count", type=int, default=50,
                       help="限制测试问题数量 (默认: 50)")
    
    args = parser.parse_args()
    
    # 确保测试数据集存在
    if not Path("evaluation_test_dataset.json").exists():
        print("测试数据集不存在，先运行 create_test_dataset.py")
        subprocess.run([sys.executable, "create_test_dataset.py"])
    
    # 运行评估
    evaluator = SystemEvaluator(
        max_test_count=args.test_count,
        skip_baseline=args.skip_baseline
    )
    evaluator.run_full_evaluation()