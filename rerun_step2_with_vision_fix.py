#!/usr/bin/env python3
"""
重新运行步骤2：将content_list.json转为page_content.json
应用Vision增强描述的修复
"""

import os
from pathlib import Path
import json
from collections import defaultdict
import asyncio
from image_utils.async_image_analysis import AsyncImageAnalysis
from dotenv import load_dotenv
load_dotenv()

def group_by_page(content_list):
    pages = defaultdict(list)
    for item in content_list:
        page_idx = item.get('page_idx', 0)
        pages[page_idx].append(item)
    return dict(pages)

def item_to_markdown(item, enable_image_caption=True):
    """
    修复后的版本：正确应用Vision增强的描述
    """
    vision_provider = "guiji" 
    vision_model = os.getenv("LOCAL_VISION_MODEL", "Pro/Qwen/Qwen2.5-VL-7B-Instruct")
    vision_api_key = os.getenv("LOCAL_API_KEY")
    vision_base_url = os.getenv("LOCAL_BASE_URL")
    
    if enable_image_caption:
        print(f"Vision配置: provider={vision_provider}, model={vision_model}")
        print(f"API检查: key存在={bool(vision_api_key)}, base_url={vision_base_url}")
    
    if item['type'] == 'text':
        level = item.get('text_level', 0)
        text = item.get('text', '')
        if level == 1:
            return f"# {text}\n\n"
        elif level == 2:
            return f"## {text}\n\n"
        else:
            return f"{text}\n\n"
    elif item['type'] == 'image':
        captions = item.get('image_caption', [])
        caption = captions[0] if captions else ''
        img_path = item.get('img_path', '')
        
        # 路径修复逻辑
        absolute_img_path = img_path
        if img_path and img_path.startswith('images/'):
            base_dir = Path.cwd()
            content_dirs = list(base_dir.glob('data_base_json_content/*/*/auto'))
            for content_dir in content_dirs:
                potential_path = content_dir / img_path
                if potential_path.exists():
                    absolute_img_path = str(potential_path)
                    break
        
        should_analyze = enable_image_caption and img_path and os.path.exists(absolute_img_path)
        has_basic_caption = caption and len(caption) < 50
        
        if should_analyze and (not caption or has_basic_caption):
            try:
                print(f"正在分析图片: {absolute_img_path}")
                print(f"原始路径: {img_path}")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def get_enhanced_caption():
                    async with AsyncImageAnalysis(
                        provider=vision_provider,
                        api_key=vision_api_key,
                        base_url=vision_base_url,
                        vision_model=vision_model
                    ) as analyzer:
                        result = await analyzer.analyze_image(local_image_path=absolute_img_path)
                        enhanced_description = result.get('description', '')
                        title = result.get('title', '')
                        return enhanced_description if enhanced_description else title
                
                enhanced_caption = loop.run_until_complete(get_enhanced_caption())
                loop.close()
                
                if enhanced_caption:
                    if caption:
                        item['image_caption'] = [caption, f"AI描述: {enhanced_caption}"]
                        print(f"图片caption已增强: {enhanced_caption[:100]}...")
                        # 修复：使用增强的描述作为显示的caption
                        caption = f"{caption} - {enhanced_caption}"
                    else:
                        item['image_caption'] = [enhanced_caption]
                        print(f"图片caption已生成: {enhanced_caption[:100]}...")
                        # 修复：使用生成的描述作为caption
                        caption = enhanced_caption
                        
            except Exception as e:
                print(f"图片解释失败: {absolute_img_path}")
                print(f"原始路径: {img_path}")
                print(f"错误详情: {str(e)}")
                
        md = f"![{caption}]({img_path})\n"
        return md + "\n"
    elif item['type'] == 'table':
        captions = item.get('table_caption', [])
        caption = captions[0] if captions else ''
        table_html = item.get('table_body', '')
        img_path = item.get('img_path', '')
        md = ''
        if caption:
            md += f"**{caption}**\n"
        if img_path:
            md += f"![{caption}]({img_path})\n"
        md += f"{table_html}\n\n"
        return md
    else:
        return '\n'

def assemble_pages_to_markdown(pages):
    page_md = {}
    for page_idx in sorted(pages.keys()):
        md = ''
        for item in pages[page_idx]:
            md += item_to_markdown(item, enable_image_caption=True)
        page_md[page_idx] = md
    return page_md

def process_single_pdf(pdf_dir, output_dir):
    """处理单个PDF"""
    file_name = pdf_dir.name
    json_path = pdf_dir / 'auto' / f'{file_name}_content_list.json'
    
    if not json_path.exists():
        sub_dir = pdf_dir / file_name
        json_path2 = sub_dir / 'auto' / f'{file_name}_content_list.json'
        if json_path2.exists():
            json_path = json_path2
        else:
            print(f"未找到: {json_path} 也未找到: {json_path2}")
            return False
    
    try:
        print(f"🔄 处理: {file_name}")
        with open(json_path, 'r', encoding='utf-8') as f:
            content_list = json.load(f)
        
        pages = group_by_page(content_list)
        page_md = assemble_pages_to_markdown(pages)
        
        output_pdf_dir = output_dir / file_name
        output_pdf_dir.mkdir(parents=True, exist_ok=True)
        output_json_path = output_pdf_dir / f'{file_name}_page_content.json'
        
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(page_md, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已输出: {output_json_path}")
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_name}: {str(e)}")
        return False

def main():
    base_dir = Path(__file__).parent
    content_dir = base_dir / 'data_base_json_content'
    page_dir = base_dir / 'data_base_json_page_content'
    
    print("=" * 60)
    print("🔧 重新运行步骤2：应用Vision增强描述修复")
    print("=" * 60)
    
    if not content_dir.exists():
        print(f"❌ 输入目录不存在: {content_dir}")
        return
    
    # 获取所有PDF目录
    pdf_dirs = [d for d in content_dir.iterdir() if d.is_dir()]
    total_pdfs = len(pdf_dirs)
    
    print(f"📊 总共需要处理: {total_pdfs} 个PDF")
    print(f"🎯 输出目录: {page_dir}")
    print()
    
    success_count = 0
    
    for i, pdf_dir in enumerate(pdf_dirs, 1):
        print(f"[{i}/{total_pdfs}] ", end="")
        if process_single_pdf(pdf_dir, page_dir):
            success_count += 1
        
        # 每处理10个显示进度
        if i % 10 == 0:
            print(f"\n📈 进度: {i}/{total_pdfs} ({i/total_pdfs*100:.1f}%) | 成功: {success_count}")
    
    print("\n" + "=" * 60)
    print(f"🎉 步骤2重新处理完成！")
    print(f"📊 总数: {total_pdfs} | 成功: {success_count} | 失败: {total_pdfs - success_count}")
    
    if success_count == total_pdfs:
        print("✅ 所有文件处理成功！现在Vision增强描述应该正确显示在markdown中。")
    else:
        print(f"⚠️  有 {total_pdfs - success_count} 个文件处理失败，请检查日志。")

if __name__ == '__main__':
    main()