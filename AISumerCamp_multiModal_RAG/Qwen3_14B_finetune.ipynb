{"cells": [{"cell_type": "markdown", "id": "fb5cfea2", "metadata": {}, "source": ["# Qwen3-14B 中文指令微调代码与原理逐步详解\n", "\n", "本 notebook 结合代码和详细注释，逐步讲解 Qwen3-14B 中文指令微调的完整流程，帮助你理解每一步的作用和背后机制。"]}, {"cell_type": "markdown", "id": "85b1f9f0", "metadata": {}, "source": ["## 1. 安装依赖与环境准备\n", "\n", "在微调大模型前，需要准备好合适的硬件和软件环境，并安装相关依赖包。"]}, {"cell_type": "code", "execution_count": null, "id": "35d7716f", "metadata": {}, "outputs": [], "source": ["# 安装所有必要的依赖包\n", "# unsloth: 高效大模型微调工具\n", "# bitsandbytes: 支持8bit/4bit量化，节省显存\n", "# accelerate: 分布式训练和推理加速\n", "# xformers: 高效Transformer加速库\n", "# peft: 参数高效微调（如LoRA）核心库\n", "# trl: HuggingFace指令微调训练库\n", "# datasets: 数据集加载和处理\n", "# 其他如sentencepiece、protobuf、huggingface_hub、hf_transfer等\n", "!pip install unsloth bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo sentencepiece protobuf 'datasets>=3.4.1,<4.0.0' 'huggingface_hub>=0.34.0' hf_transfer"]}, {"cell_type": "markdown", "id": "7fc8d7ce", "metadata": {}, "source": ["## 2. 加载 Qwen3-14B 基础模型\n", "\n", "本节将加载 Qwen3-14B 基础模型，并介绍相关参数的作用。"]}, {"cell_type": "code", "execution_count": 1, "id": "fca99e6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/unsloth/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.7.11: Fast Qwen3 patching. Transformers: 4.54.1.\n", "   \\\\   /|    NVIDIA RTX A6000. Num GPUs = 1. Max memory: 44.988 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 8.6. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 3/3 [00:08<00:00,  2.75s/it]\n"]}], "source": ["# 导入 FastLanguageModel 和 torch\n", "from unsloth import FastLanguageModel\n", "import torch\n", "\n", "# 设置最大序列长度，影响模型一次能处理的最大token数\n", "max_seq_length = 2048\n", "# 自动检测数据类型（float16/bfloat16/float32），影响显存和速度\n", "dtype = None\n", "# 是否使用4bit量化以节省显存\n", "load_in_4bit = True\n", "\n", "# 加载 Qwen3-14B 基础模型和分词器\n", "# model_name 指定模型权重来源\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = 'unsloth/Qwen3-14B-Base-unsloth-bnb-4bit',\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", ")\n", "# 4bit量化能大幅降低大模型的显存需求，使消费级显卡也能运行"]}, {"cell_type": "markdown", "id": "8c435449", "metadata": {}, "source": ["## 3. 添加 LoRA 适配器（参数高效微调）\n", "\n", "LoRA（Low-Rank Adaptation）是一种参数高效微调方法，只需训练极少量新增参数即可获得良好效果。"]}, {"cell_type": "code", "execution_count": null, "id": "8f545363", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.7.11 patched 40 layers with 40 QKV layers, 40 O layers and 40 MLP layers.\n"]}], "source": ["# 使用 LoRA 只微调部分参数，节省显存和计算资源\n", "# r: Lo<PERSON>秩，越大可学习能力越强，显存消耗也越大\n", "# target_modules: 需要注入LoRA的模块 \n", "# q_proj, k_proj, v_proj, o_proj：分别是注意力机制中的 Query、Key、Value 和输出投影层。\n", "# gate_proj, up_proj, down_proj：是前馈网络（FFN）中的门控、上投影和下投影层。\n", "# lora_alpha: Lo<PERSON>缩放因子\n", "# lora_dropout: LoRA dropout，0表示不丢弃\n", "# bias: 不训练bias\n", "# use_gradient_checkpointing: 节省显存\n", "# random_state: 随机种子，保证可复现\n", "# use_rslora/loftq_config: 进阶参数\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16,\n", "    target_modules = ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj'],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0,\n", "    bias = 'none',\n", "    use_gradient_checkpointing = 'unsloth',\n", "    random_state = 3407,\n", "    use_rslora = False,\n", "    loftq_config = None,\n", ")\n", "# LoRA只需训练极少量新增参数，极大降低资源消耗"]}, {"cell_type": "markdown", "id": "9a961979", "metadata": {}, "source": ["## 4. 数据准备与 Prompt 格式化\n", "\n", "本节将介绍如何准备指令微调数据，并将其格式化为适合模型训练的 prompt 格式。"]}, {"cell_type": "code", "execution_count": 3, "id": "85ffb2db", "metadata": {}, "outputs": [], "source": ["# 定义中文 Alpaca prompt 模板，统一格式便于模型学习指令跟随能力\n", "alpaca_prompt = '''下面是一个任务指令，配有进一步的输入信息，请写出一个合理的完成该任务的回复。\n", "\n", "### 指令:\n", "{}\n", "\n", "### 输入:\n", "{}\n", "\n", "### 回复:\n", "{}'''\n", "\n", "# 获取分词器的 EOS（结束）标记，帮助模型区分样本\n", "EOS_TOKEN = tokenizer.eos_token\n", "\n", "# 格式化数据集为 prompt 格式\n", "# 将原始三元组拼接为统一文本，便于模型训练\n", "def formatting_prompts_func(examples):\n", "    instructions = examples['instruction']\n", "    inputs = examples['input']\n", "    outputs = examples['output']\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        text = alpaca_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return { 'text': texts }\n", "\n", "# 加载 Alpaca 中文数据集（52K条指令-输入-输出样本）\n", "from datasets import load_dataset\n", "dataset = load_dataset('yahma/alpaca-cleaned', split='train')\n", "# 应用格式化函数，得到最终可训练数据\n", "dataset = dataset.map(formatting_prompts_func, batched=True)"]}, {"cell_type": "markdown", "id": "8e654ebd", "metadata": {}, "source": ["## 5. 开始训练\n", "\n", "本节将使用 SFTTrainer 进行指令微调，并解释主要训练参数的作用。"]}, {"cell_type": "code", "execution_count": null, "id": "59dfde36", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 51,760 | Num Epochs = 1 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 64,225,280 of 14,832,532,480 (0.43% trained)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 03:51, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.347000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.810600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.471400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.678400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.519800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.429700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.050000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.233500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.124800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.017100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.808700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.848200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.770100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.981200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.762600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.731000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.878200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.234600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.900700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.732200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.803100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.878200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.883200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.893100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.965900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.915900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.959300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.811400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.855600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.702200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.784000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.759700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.918300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.761900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.869500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.767600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.742600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.662400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.975800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.987200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.826900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.862500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.809900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.826400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.916500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.851300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.708600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.130400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.767100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.980800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.937200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.806000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.908200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>1.032400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.696500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.918100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.772400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.699800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.743400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.833200</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 导入 SFTConfig 和 SFTTrainer\n", "from trl import SFTConfig, SFTTrainer\n", "\n", "# 配置并初始化 Trainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = 'text',\n", "    max_seq_length = max_seq_length,\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,  # 每张卡 batch size，影响显存和速度\n", "        gradient_accumulation_steps = 4,  # 梯度累积，等效增大batch size\n", "        warmup_steps = 5,                # 预热步数\n", "        max_steps = 60,                  # 训练步数（可根据需要调整）\n", "        learning_rate = 2e-4,            # 学习率\n", "        logging_steps = 1,               # 日志打印频率\n", "        optim = 'adamw_8bit',            # 优化器，适合大模型\n", "        weight_decay = 0.01,             # 权重衰减\n", "        lr_scheduler_type = 'linear',    # 学习率调度\n", "        seed = 3407,                     # 随机种子\n", "        output_dir = 'model_train_outputs',          # 输出目录\n", "        report_to = 'none',              # 不上报日志\n", "    ),\n", ")\n", "# 开始训练\n", "trainer_stats = trainer.train()"]}, {"cell_type": "markdown", "id": "64a05a09", "metadata": {}, "source": ["## 6. 推理测试\n", "\n", "训练完成后，可以用微调后的模型进行推理，检验其指令跟随能力。"]}, {"cell_type": "code", "execution_count": 5, "id": "26384502", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['下面是一个任务指令，配有进一步的输入信息，请写出一个合理的完成该任务的回复。\\n\\n### 指令:\\n请续写斐波那契数列。\\n\\n### 输入:\\n1, 1, 2, 3, 5, 8\\n\\n### 回复:\\n13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6']\n"]}], "source": ["# 切换到推理模式，提升推理速度\n", "FastLanguageModel.for_inference(model)\n", "\n", "# 构造推理输入，使用与训练相同的prompt模板\n", "inputs = tokenizer([\n", "    alpaca_prompt.format(\n", "        '请续写斐波那契数列。',\n", "        '1, 1, 2, 3, 5, 8',\n", "        ''\n", "    )\n", "], return_tensors='pt').to('cuda')\n", "\n", "# 生成模型输出，max_new_tokens控制生成长度，use_cache加速推理\n", "outputs = model.generate(**inputs, max_new_tokens=64, use_cache=True)\n", "# 解码输出并打印\n", "print(tokenizer.batch_decode(outputs))"]}, {"cell_type": "markdown", "id": "8fea76e8", "metadata": {}, "source": ["## 7. 保存微调后的模型\n", "\n", "最后，将微调得到的 LoRA 权重和分词器保存，便于后续加载和部署。"]}, {"cell_type": "code", "execution_count": null, "id": "cc7c0e42", "metadata": {}, "outputs": [], "source": ["# 保存 LoRA 微调权重和分词器\n", "# 只需保存LoRA权重，文件体积小，便于分发和部署\n", "model.save_pretrained('lora_model')\n", "tokenizer.save_pretrained('lora_model')\n", "# 后续加载时，只需基础模型+LoRA权重+分词器即可复现微调效果"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}