  2. 建议添加的性能指标体系

  A. 检索效果指标 (最关键)

  # 文件: evaluation_metrics.py
  class RetrievalMetrics:
      - Hit@K: 前K个结果中包含正确答案的比例
      - NDCG@K: 归一化折损累积增益
      - MRR: 平均倒数排名
      - Precision@K: 前K个结果的准确率
      - Recall@K: 前K个结果的召回率

  B. 生成质量指标

  class GenerationMetrics:
      - BLEU/ROUGE: 与标准答案的文本相似度
      - 事实准确性: 数值、日期、公司名等准确率
      - 引用准确性: 来源文件和页码是否正确
      - 答案完整性: 是否回答了问题的所有方面
      - 可读性评分: 语言流畅度和逻辑性

  C. 系统性能指标

  class SystemMetrics:
      - 检索延迟: Dense/Sparse/Rerank各阶段耗时
      - 生成延迟: LLM推理时间
      - 总响应时间: 端到端延迟
      - 吞吐量: QPS (queries per second)
      - 资源使用: GPU显存、CPU、内存占用

  D. Vision增强效果指标

  class VisionMetrics:
      - Vision覆盖率: 图片描述的完整度
      - 图表查询准确率: 涉及图表的问题回答准确性
      - Vision-Text融合效果: 图文结合查询的表现