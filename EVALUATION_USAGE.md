# RAG评估系统使用指南

## 快速开始

### 1. 首次完整评估
```bash
# 第一次运行，包含Baseline和改进系统评估
python run_evaluation.py
```

### 2. 跳过Baseline评估（推荐）
```bash
# 使用现有Baseline结果，只评估新的改进版本
python run_evaluation.py --skip-baseline
```

### 3. 自定义测试数量
```bash
# 限制测试20个问题，跳过Baseline
python run_evaluation.py --skip-baseline --test-count 20

# 完整100个问题测试
python run_evaluation.py --skip-baseline --test-count 100
```

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--skip-baseline` | 跳过Baseline系统评估，使用现有结果 | False |
| `--test-count` | 限制测试问题数量 | 50 |

## 输出文件

### 第一次运行（完整评估）
- `baseline_detailed_results.json` - Baseline系统详细结果
- `improved_detailed_results2.json` - 改进系统详细结果
- `evaluation_comparison_report2.json` - 对比分析报告
- `evaluation_results_comparison2.png` - 可视化图表

### 后续运行（跳过Baseline）
- 复用现有的 `baseline_detailed_results.json`
- 生成新的 `improved_detailed_results2.json`
- 更新 `evaluation_comparison_report2.json`
- 更新 `evaluation_results_comparison2.png`

## 时间估算

| 模式 | 50个问题 | 100个问题 |
|------|----------|-----------|
| 完整评估 | ~70分钟 | ~140分钟 |
| 跳过Baseline | ~35分钟 | ~70分钟 |

## 版本管理

当你有新的优化版本时：

1. 创建新的RAG脚本（如 `rag_from_page_chunks5.py`）
2. 修改 `run_evaluation.py` 中的导入：
   ```python
   from rag_from_page_chunks5 import OptimizedAdvancedRAG
   ```
3. 更新输出文件名为版本3：
   ```python
   "improved_detailed_results3.json"
   "evaluation_comparison_report3.json"
   "evaluation_results_comparison3.png"
   ```

## 建议的工作流程

1. **首次评估**：运行完整评估建立Baseline
   ```bash
   python run_evaluation.py --test-count 50
   ```

2. **后续优化测试**：跳过Baseline节省时间
   ```bash
   python run_evaluation.py --skip-baseline --test-count 50
   ```

3. **最终验证**：完整100个问题测试
   ```bash
   python run_evaluation.py --skip-baseline --test-count 100
   ```

## 注意事项

- 首次运行必须生成Baseline结果
- `--skip-baseline` 需要存在 `baseline_detailed_results.json` 文件
- 如果Baseline文件不存在，会自动执行完整评估
- 测试数量变化时，需要重新生成Baseline（确保对比公平）