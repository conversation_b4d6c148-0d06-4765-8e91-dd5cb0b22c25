#!/usr/bin/env python3
"""
快速测试优化版本RAG系统
"""

from rag_from_page_chunks4 import OptimizedAdvancedRAG

def test_single_question():
    """测试单个问题"""
    print("初始化优化版本RAG系统...")
    rag = OptimizedAdvancedRAG("all_pdf_page_chunks.json")
    rag.setup()
    
    # 测试问题
    question = "千味央厨：2020年家庭用速冻调理食品的产量占比是多少？"
    print(f"\n测试问题: {question}")
    
    try:
        result = rag.generate_answer(question)
        print(f"\n✅ 成功回答:")
        print(f"答案: {result['answer']}")
        print(f"来源: {result['filename']}")
        print(f"页码: {result['page']}")
        print(f"问题类型: {result['question_type']}")
        print(f"检索策略: {result['retrieval_strategy']}")
        print(f"使用chunks数: {result['chunks_used']}")
        print(f"缓存命中: {result['cache_hit']}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_question()